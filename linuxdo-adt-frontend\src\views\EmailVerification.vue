<template>
  <div class="verification-container">
    <el-card class="verification-card">
      <div class="verification-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="status-section">
          <el-icon class="loading-icon" size="48"><Loading /></el-icon>
          <h2>正在验证邮箱...</h2>
          <p>请稍候，我们正在验证您的邮箱地址。</p>
        </div>

        <!-- 验证成功 -->
        <div v-else-if="verificationResult.success" class="status-section success">
          <el-icon class="status-icon" size="48"><CircleCheck /></el-icon>
          <h2>邮箱验证成功！</h2>
          <p>您的邮箱 <strong>{{ verificationResult.email }}</strong> 已成功验证。</p>
          <div class="action-buttons">
            <el-button type="primary" @click="goToLogin">
              前往登录
            </el-button>
            <el-button @click="goToProfile">
              个人中心
            </el-button>
          </div>
        </div>

        <!-- 验证失败 -->
        <div v-else class="status-section error">
          <el-icon class="status-icon" size="48"><CircleClose /></el-icon>
          <h2>邮箱验证失败</h2>
          <p class="error-message">{{ verificationResult.message }}</p>
          <div class="error-details">
            <p>可能的原因：</p>
            <ul>
              <li>验证链接已过期（有效期24小时）</li>
              <li>验证链接已被使用</li>
              <li>验证链接格式不正确</li>
            </ul>
          </div>
          <div class="action-buttons">
            <el-button type="primary" @click="goToProfile">
              个人中心
            </el-button>
            <el-button @click="goToLogin">
              重新登录
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import api from '../api/index'

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const verificationResult = ref({
  success: false,
  message: '',
  email: ''
})

// 验证邮箱
const verifyEmail = async () => {
  const token = route.query.token

  if (!token) {
    verificationResult.value = {
      success: false,
      message: '验证链接无效：缺少验证令牌',
      email: ''
    }
    loading.value = false
    return
  }

  try {
    const response = await api.post('/api/auth/verify-email', null, {
      params: { token }
    })

    verificationResult.value = {
      success: true,
      message: '邮箱验证成功',
      email: response.data.email
    }

    ElMessage.success('邮箱验证成功！')
  } catch (error) {
    let errorMessage = '验证失败，请稍后重试'
    
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    }

    verificationResult.value = {
      success: false,
      message: errorMessage,
      email: ''
    }

    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 前往登录页面
const goToLogin = () => {
  router.push('/admin/login')
}

// 前往个人中心
const goToProfile = () => {
  const token = localStorage.getItem('access_token')
  if (token) {
    router.push('/admin/profile')
  } else {
    router.push('/admin/login')
  }
}

// 组件挂载时开始验证
onMounted(() => {
  verifyEmail()
})
</script>

<style scoped>
.verification-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.verification-card {
  width: 100%;
  max-width: 500px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.verification-content {
  padding: 40px 30px;
}

.status-section {
  text-align: center;
}

.loading-icon {
  color: #409eff;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.status-icon {
  margin-bottom: 20px;
}

.success .status-icon {
  color: #67c23a;
}

.error .status-icon {
  color: #f56c6c;
}

.status-section h2 {
  margin: 20px 0 15px 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.status-section p {
  margin: 10px 0;
  color: #666;
  font-size: 16px;
  line-height: 1.5;
}

.error-message {
  color: #f56c6c;
  font-weight: 500;
}

.error-details {
  margin: 20px 0;
  text-align: left;
  background: #fef0f0;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #f56c6c;
}

.error-details p {
  margin: 0 0 10px 0;
  color: #f56c6c;
  font-weight: 500;
}

.error-details ul {
  margin: 0;
  padding-left: 20px;
}

.error-details li {
  margin: 5px 0;
  color: #666;
}

.action-buttons {
  margin-top: 30px;
  display: flex;
  gap: 15px;
  justify-content: center;
}

.action-buttons .el-button {
  padding: 12px 24px;
  font-size: 16px;
}
</style>
