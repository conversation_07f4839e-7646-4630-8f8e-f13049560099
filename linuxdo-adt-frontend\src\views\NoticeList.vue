<template>
  <div class="notice-list">
    <div class="header-actions">
      <h3>系统提示管理</h3>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        添加提示
      </el-button>
    </div>

    <!-- 搜索和过滤区域 -->
    <div class="search-filters">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchText"
            placeholder="搜索标题或内容"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="filterPriority"
            placeholder="优先级"
            clearable
            @change="handleFilter"
          >
            <el-option label="低" value="low" />
            <el-option label="普通" value="info" />
            <el-option label="重要" value="warning" />
            <el-option label="紧急" value="error" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="filterStatus"
            placeholder="状态"
            clearable
            @change="handleFilter"
          >
            <el-option label="启用" value="true" />
            <el-option label="禁用" value="false" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="resetFilters">重置</el-button>
        </el-col>
      </el-row>
    </div>
    
    <el-table :data="notices" v-loading="loading" style="width: 100%">
      <el-table-column label="序号" width="80" type="index" :index="(index: number) => index + 1" />
      <el-table-column prop="title" label="标题" width="200" show-overflow-tooltip />
      <el-table-column prop="content" label="内容" min-width="250" show-overflow-tooltip />
      <el-table-column prop="creator_username" label="创建者" width="120" />
      <el-table-column prop="priority" label="优先级" width="100">
        <template #default="scope">
          <el-tag :type="getPriorityType(scope.row.priority)">
            {{ getPriorityText(scope.row.priority) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="is_active" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
            {{ scope.row.is_active ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <!-- 管理员可以编辑所有提示，普通用户只能编辑自己创建的提示 -->
          <el-button
            v-if="canEditNotice(scope.row)"
            size="small"
            @click="editNotice(scope.row)"
          >
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <!-- 管理员可以删除所有提示，普通用户只能删除自己创建的提示 -->
          <el-button
            v-if="canDeleteNotice(scope.row)"
            size="small"
            type="danger"
            @click="deleteNotice(scope.row)"
          >
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 创建/编辑提示对话框 -->
    <el-dialog v-model="showCreateDialog" :title="editingNotice ? '编辑提示' : '添加提示'" width="600px">
      <el-form :model="noticeForm" :rules="noticeRules" ref="noticeFormRef" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="noticeForm.title" placeholder="请输入提示标题" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input v-model="noticeForm.content" type="textarea" :rows="4" placeholder="请输入提示内容" />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="noticeForm.priority" placeholder="请选择优先级" style="width: 100%">
            <el-option label="信息" value="info" />
            <el-option label="警告" value="warning" />
            <el-option label="错误" value="error" />
            <el-option label="成功" value="success" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-switch v-model="noticeForm.is_active" active-text="启用" inactive-text="禁用" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelEdit">取消</el-button>
        <el-button type="primary" @click="handleSaveNotice" :loading="saving">
          {{ editingNotice ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import api from '../api'

interface SystemNotice {
  id: number
  title: string
  content: string
  priority: string
  is_active: boolean
  creator_id: number
  creator_username?: string
  created_at: string
  updated_at?: string
}

interface NoticeForm {
  title: string
  content: string
  priority: string
  is_active: boolean
}

const notices = ref<SystemNotice[]>([])
const allNotices = ref<SystemNotice[]>([]) // 存储所有提示数据
const loading = ref(false)
const showCreateDialog = ref(false)
const saving = ref(false)
const editingNotice = ref<SystemNotice | null>(null)

// 当前用户信息
const currentUser = ref<any>(null)
const isAdmin = ref(false)

// 搜索和过滤相关
const searchText = ref('')
const filterPriority = ref('')
const filterStatus = ref('')

const noticeForm = reactive<NoticeForm>({
  title: '',
  content: '',
  priority: 'info',
  is_active: true
})

const noticeRules = {
  title: [{ required: true, message: '请输入提示标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入提示内容', trigger: 'blur' }]
}

const noticeFormRef = ref()

const loadNotices = async () => {
  loading.value = true
  try {
    const response = await api.get<SystemNotice[]>('/api/notices')
    allNotices.value = response.data
    applyFilters()
  } catch (error) {
    ElMessage.error('加载提示列表失败')
  } finally {
    loading.value = false
  }
}

// 应用过滤和搜索
const applyFilters = () => {
  let filteredNotices = [...allNotices.value]

  // 搜索过滤
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase()
    filteredNotices = filteredNotices.filter(notice =>
      notice.title.toLowerCase().includes(searchLower) ||
      notice.content.toLowerCase().includes(searchLower)
    )
  }

  // 优先级过滤
  if (filterPriority.value) {
    filteredNotices = filteredNotices.filter(notice => notice.priority === filterPriority.value)
  }

  // 状态过滤
  if (filterStatus.value) {
    const isActive = filterStatus.value === 'true'
    filteredNotices = filteredNotices.filter(notice => notice.is_active === isActive)
  }

  notices.value = filteredNotices
}

// 搜索处理
const handleSearch = () => {
  applyFilters()
}

// 过滤处理
const handleFilter = () => {
  applyFilters()
}

// 重置过滤器
const resetFilters = () => {
  searchText.value = ''
  filterPriority.value = ''
  filterStatus.value = ''
  applyFilters()
}

const editNotice = (notice: SystemNotice) => {
  editingNotice.value = notice
  Object.assign(noticeForm, {
    title: notice.title,
    content: notice.content,
    priority: notice.priority || 'info',
    is_active: notice.is_active
  })
  showCreateDialog.value = true
}

const cancelEdit = () => {
  showCreateDialog.value = false
  editingNotice.value = null
  Object.assign(noticeForm, { title: '', content: '', priority: 'info', is_active: true })
}

const handleSaveNotice = async () => {
  if (!noticeFormRef.value) return
  
  await noticeFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      saving.value = true
      try {
        if (editingNotice.value) {
          await api.put(`/api/notices/${editingNotice.value.id}`, noticeForm)
          ElMessage.success('提示更新成功')
        } else {
          await api.post('/api/notices', noticeForm)
          ElMessage.success('提示创建成功')
        }
        
        cancelEdit()
        loadNotices()
      } catch (error: any) {
        ElMessage.error(error.response?.data?.detail || '操作失败')
      } finally {
        saving.value = false
      }
    }
  })
}

const deleteNotice = async (notice: SystemNotice) => {
  try {
    await ElMessageBox.confirm('确定要删除这个提示吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await api.delete(`/api/notices/${notice.id}`)
    ElMessage.success('提示删除成功')
    loadNotices()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除提示失败')
    }
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getPriorityType = (priority: string) => {
  const typeMap: Record<string, string> = {
    'info': 'info',
    'warning': 'warning',
    'error': 'danger',
    'success': 'success'
  }
  return typeMap[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const textMap: Record<string, string> = {
    'info': '信息',
    'warning': '警告',
    'error': '错误',
    'success': '成功'
  }
  return textMap[priority] || '信息'
}

// 获取当前用户信息
const getCurrentUser = async () => {
  try {
    const response = await api.get('/api/auth/me')
    currentUser.value = response.data
    isAdmin.value = response.data.role === 'admin'
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 权限判断：是否可以编辑提示
const canEditNotice = (notice: SystemNotice) => {
  return isAdmin.value || (currentUser.value && notice.creator_id === currentUser.value.id)
}

// 权限判断：是否可以删除提示
const canDeleteNotice = (notice: SystemNotice) => {
  return isAdmin.value || (currentUser.value && notice.creator_id === currentUser.value.id)
}

onMounted(async () => {
  await getCurrentUser()
  loadNotices()
})
</script>

<style scoped>
.notice-list {
  background: white;
  padding: 20px;
  border-radius: 8px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions h3 {
  margin: 0;
}

/* 搜索过滤区域样式 */
.search-filters {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}
</style>
