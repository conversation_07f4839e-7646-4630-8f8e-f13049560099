import api from './index'

export interface Task {
  id: number
  title: string
  description?: string
  task_type: string
  duration_days: number
  status: string
  share_token: string
  creator_id: number
  created_at: string
  updated_at?: string
  expires_at?: string
}

export interface TaskCreate {
  title: string
  description?: string
  task_type: string
  duration_days: number
}

export interface TaskUpdate {
  title?: string
  description?: string
  task_type?: string
  duration_days?: number
  status?: string
}

// 获取任务列表
export const getTasks = (skip = 0, limit = 100) => {
  return api.get<Task[]>('/api/tasks', {
    params: { skip, limit }
  })
}

// 创建任务
export const createTask = (data: TaskCreate) => {
  return api.post<Task>('/api/tasks', data)
}

// 获取任务详情
export const getTask = (taskId: number) => {
  return api.get<Task>(`/api/tasks/${taskId}`)
}

// 更新任务
export const updateTask = (taskId: number, data: TaskUpdate) => {
  return api.put<Task>(`/api/tasks/${taskId}`, data)
}

// 删除任务
export const deleteTask = (taskId: number) => {
  return api.delete(`/api/tasks/${taskId}`)
}

// 重新生成分享链接
export const regenerateShareToken = (taskId: number) => {
  return api.post<{ share_token: string; share_url: string }>(`/api/tasks/${taskId}/share`)
}
