<template>
  <div class="user-management">
    <div class="header-section">
      <h2>用户管理</h2>
      <div class="header-actions">
        <el-input
          v-model="searchText"
          placeholder="搜索用户名或邮箱"
          clearable
          style="width: 300px; margin-right: 10px"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加用户
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards" v-if="stats">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.total_users }}</div>
          <div class="stat-label">总用户数</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.active_users }}</div>
          <div class="stat-label">活跃用户</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.admin_users }}</div>
          <div class="stat-label">管理员</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.verified_users }}</div>
          <div class="stat-label">已验证邮箱</div>
        </div>
      </el-card>
    </div>

    <!-- 用户列表 -->
    <el-table :data="users" style="width: 100%" v-loading="loading">
      <el-table-column label="序号" width="80" type="index" :index="(index) => index + 1" />
      <el-table-column prop="username" label="用户名" width="150" />
      <el-table-column prop="email" label="邮箱" min-width="200" />
      <el-table-column prop="role" label="角色" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.role === 'admin' ? 'danger' : 'primary'">
            {{ scope.row.role === 'admin' ? '管理员' : '普通用户' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="is_active" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
            {{ scope.row.is_active ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="email_verified" label="邮箱验证" width="120">
        <template #default="scope">
          <el-tag :type="scope.row.email_verified ? 'success' : 'warning'">
            {{ scope.row.email_verified ? '已验证' : '未验证' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            @click="editUser(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            :type="scope.row.is_active ? 'warning' : 'success'"
            size="small"
            @click="toggleUserStatus(scope.row)"
          >
            {{ scope.row.is_active ? '禁用' : '启用' }}
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="deleteUser(scope.row)"
            :disabled="scope.row.id === currentUserId"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建用户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="添加用户"
      width="500px"
      @close="resetCreateForm"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="createForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="createForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="createForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="createForm.role" placeholder="请选择角色">
            <el-option label="普通用户" value="user" />
            <el-option label="管理员" value="admin" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-switch v-model="createForm.is_active" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreateUser" :loading="creating">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 编辑用户对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑用户"
      width="500px"
      @close="resetEditForm"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input
            v-model="editForm.password"
            type="password"
            placeholder="留空则不修改密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="editForm.role" placeholder="请选择角色">
            <el-option label="普通用户" value="user" />
            <el-option label="管理员" value="admin" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-switch v-model="editForm.is_active" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleEditUser" :loading="editing">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '../api/index'

// 响应式数据
const users = ref([])
const stats = ref(null)
const loading = ref(false)
const creating = ref(false)
const editing = ref(false)
const searchText = ref('')
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const currentUserId = ref(null)

// 表单引用
const createFormRef = ref()
const editFormRef = ref()

// 创建用户表单
const createForm = reactive({
  username: '',
  email: '',
  password: '',
  role: 'user',
  is_active: true
})

// 编辑用户表单
const editForm = reactive({
  id: null,
  username: '',
  email: '',
  password: '',
  role: 'user',
  is_active: true
})

// 表单验证规则
const createRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少 6 个字符', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

const editRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { min: 6, message: '密码长度至少 6 个字符', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 获取当前用户ID
const getCurrentUserId = async () => {
  try {
    // 调用API获取当前用户信息
    const response = await api.get('/api/auth/me')
    currentUserId.value = response.data.id
  } catch (error) {
    console.error('获取当前用户信息失败:', error)
    // 如果获取失败，设置为null，这样不会禁用删除按钮
    currentUserId.value = null
  }
}

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 加载用户列表
const loadUsers = async (search = '') => {
  loading.value = true
  try {
    const response = await api.get('/api/users/', {
      params: { search, limit: 1000 }
    })
    users.value = response.data
  } catch (error) {
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 加载统计信息
const loadStats = async () => {
  try {
    const response = await api.get('/api/users/stats/summary')
    stats.value = response.data
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

// 搜索处理
const handleSearch = () => {
  loadUsers(searchText.value)
}

// 重置创建表单
const resetCreateForm = () => {
  Object.assign(createForm, {
    username: '',
    email: '',
    password: '',
    role: 'user',
    is_active: true
  })
  createFormRef.value?.resetFields()
}

// 重置编辑表单
const resetEditForm = () => {
  Object.assign(editForm, {
    id: null,
    username: '',
    email: '',
    password: '',
    role: 'user',
    is_active: true
  })
  editFormRef.value?.resetFields()
}

// 创建用户
const handleCreateUser = async () => {
  if (!createFormRef.value) return
  
  try {
    await createFormRef.value.validate()
    creating.value = true
    
    await api.post('/api/users/', createForm)
    ElMessage.success('用户创建成功')
    showCreateDialog.value = false
    resetCreateForm()
    loadUsers(searchText.value)
    loadStats()
  } catch (error) {
    if (error.response?.data?.detail) {
      ElMessage.error(error.response.data.detail)
    } else {
      ElMessage.error('创建用户失败')
    }
  } finally {
    creating.value = false
  }
}

// 编辑用户
const editUser = (user) => {
  Object.assign(editForm, {
    id: user.id,
    username: user.username,
    email: user.email,
    password: '',
    role: user.role,
    is_active: user.is_active
  })
  showEditDialog.value = true
}

// 处理编辑用户
const handleEditUser = async () => {
  if (!editFormRef.value) return
  
  try {
    await editFormRef.value.validate()
    editing.value = true
    
    const updateData = {
      username: editForm.username,
      email: editForm.email,
      role: editForm.role,
      is_active: editForm.is_active
    }
    
    // 只有输入了密码才更新密码
    if (editForm.password) {
      updateData.password = editForm.password
    }
    
    await api.put(`/api/users/${editForm.id}`, updateData)
    ElMessage.success('用户更新成功')
    showEditDialog.value = false
    resetEditForm()
    loadUsers(searchText.value)
    loadStats()
  } catch (error) {
    if (error.response?.data?.detail) {
      ElMessage.error(error.response.data.detail)
    } else {
      ElMessage.error('更新用户失败')
    }
  } finally {
    editing.value = false
  }
}

// 切换用户状态
const toggleUserStatus = async (user) => {
  try {
    const action = user.is_active ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}用户 "${user.username}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await api.post(`/api/users/${user.id}/toggle-status`)
    ElMessage.success(`用户已${action}`)
    loadUsers(searchText.value)
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      if (error.response?.data?.detail) {
        ElMessage.error(error.response.data.detail)
      } else {
        ElMessage.error('操作失败')
      }
    }
  }
}

// 删除用户
const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await api.delete(`/api/users/${user.id}`)
    ElMessage.success('用户已删除')
    loadUsers(searchText.value)
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      if (error.response?.data?.detail) {
        ElMessage.error(error.response.data.detail)
      } else {
        ElMessage.error('删除失败')
      }
    }
  }
}

// 组件挂载时加载数据
onMounted(async () => {
  await getCurrentUserId()
  loadUsers()
  loadStats()
})
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-section h2 {
  margin: 0;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  align-items: center;
}

.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  text-align: center;
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}
</style>
