import api from './index'

export interface LoginForm {
  username: string
  password: string
}

export interface RegisterForm {
  username: string
  email: string
  password: string
}

export interface User {
  id: number
  username: string
  email: string
  is_active: boolean
  created_at: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
}

// 用户登录
export const login = (data: LoginForm) => {
  const formData = new FormData()
  formData.append('username', data.username)
  formData.append('password', data.password)
  
  return api.post<LoginResponse>('/api/auth/login', formData)
}

// 用户注册
export const register = (data: RegisterForm) => {
  return api.post<User>('/api/auth/register', data)
}
