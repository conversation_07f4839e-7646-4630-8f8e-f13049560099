<template>
  <div class="hitokoto-list">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>一言数据管理</span>
          <el-button type="primary" @click="loadStats">刷新统计</el-button>
        </div>
      </template>

      <!-- 统计信息 -->
      <div class="stats-section" v-if="stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总数量" :value="stats.total_count" />
          </el-col>
          <el-col :span="18">
            <div class="type-distribution">
              <h4>类型分布</h4>
              <el-tag 
                v-for="(count, type) in stats.type_distribution" 
                :key="type" 
                style="margin-right: 10px; margin-bottom: 5px;"
              >
                {{ getTypeText(type) }}: {{ count }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </div>

      <el-divider />

      <!-- 搜索和过滤区域 -->
      <div class="search-filters">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchText"
              placeholder="搜索一言内容或出处"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="filterType"
              placeholder="类型"
              clearable
              @change="handleFilter"
            >
              <el-option label="动画" value="a" />
              <el-option label="漫画" value="b" />
              <el-option label="游戏" value="c" />
              <el-option label="文学" value="d" />
              <el-option label="原创" value="e" />
              <el-option label="网络" value="f" />
              <el-option label="其他" value="g" />
              <el-option label="影视" value="h" />
              <el-option label="诗词" value="i" />
              <el-option label="网易云" value="j" />
              <el-option label="哲学" value="k" />
              <el-option label="抖机灵" value="l" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-input
              v-model="filterCreator"
              placeholder="添加者"
              clearable
              @input="handleSearch"
            />
          </el-col>
          <el-col :span="4">
            <el-input-number
              v-model="filterMinLength"
              placeholder="最小长度"
              :min="1"
              :max="1000"
              @change="handleFilter"
              style="width: 100%"
            />
          </el-col>
          <el-col :span="4">
            <el-button @click="resetFilters">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 一言列表 -->
      <el-table :data="hitokotoList" v-loading="loading" style="width: 100%">
        <el-table-column label="序号" width="80" type="index" :index="(index: number) => (currentPage - 1) * pageSize + index + 1" />
        <el-table-column prop="hitokoto" label="一言内容" min-width="300" show-overflow-tooltip>
          <template #default="scope">
            <div class="hitokoto-content">
              {{ scope.row.hitokoto }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="type" label="类型" width="80">
          <template #default="scope">
            <el-tag size="small">{{ getTypeText(scope.row.type) }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="from_text" label="出处" width="150" show-overflow-tooltip>
          <template #default="scope">
            <div v-if="scope.row.from_text" class="from-info">
              <div>{{ scope.row.from_text }}</div>
              <div v-if="scope.row.from_who" class="from-who">—— {{ scope.row.from_who }}</div>
            </div>
            <span v-else class="text-muted">未知</span>
          </template>
        </el-table-column>

        <el-table-column prop="creator" label="添加者" width="100" />

        <el-table-column prop="length" label="长度" width="80" />

        <el-table-column prop="created_at" label="收录时间" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="140" fixed="right">
          <template #default="scope">
            <div style="display: flex; gap: 8px;">
              <el-button
                type="primary"
                size="small"
                @click="viewDetail(scope.row)"
              >
                查看
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteHitokoto(scope.row.id)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="一言详情" width="600px">
      <div v-if="selectedHitokoto" class="detail-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="一言内容">
            {{ selectedHitokoto.hitokoto }}
          </el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag>{{ getTypeText(selectedHitokoto.type) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="出处">
            {{ selectedHitokoto.from_text || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="作者">
            {{ selectedHitokoto.from_who || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="添加者">
            {{ selectedHitokoto.creator || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="句子长度">
            {{ selectedHitokoto.length }}
          </el-descriptions-item>
          <el-descriptions-item label="UUID">
            <el-link 
              :href="`https://hitokoto.cn/?uuid=${selectedHitokoto.uuid}`" 
              target="_blank"
              type="primary"
            >
              {{ selectedHitokoto.uuid }}
            </el-link>
          </el-descriptions-item>
          <el-descriptions-item label="收录时间">
            {{ formatDate(selectedHitokoto.created_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import api from '../api/index'

// 数据
const hitokotoList = ref([])
const allHitokotoList = ref([]) // 存储所有一言数据
const stats = ref(null)
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(50)
const total = ref(0)

// 搜索和过滤相关
const searchText = ref('')
const filterType = ref('')
const filterCreator = ref('')
const filterMinLength = ref<number | null>(null)

// 对话框
const showDetailDialog = ref(false)
const selectedHitokoto = ref(null)

// 类型映射
const typeMap = {
  'a': '动画',
  'b': '漫画', 
  'c': '游戏',
  'd': '文学',
  'e': '原创',
  'f': '来自网络',
  'g': '其他',
  'h': '影视',
  'i': '诗词',
  'j': '网易云',
  'k': '哲学',
  'l': '抖机灵'
}

// 获取类型文本
const getTypeText = (type: string) => {
  return typeMap[type] || type || '未知'
}

// 加载一言列表
const loadHitokotoList = async () => {
  loading.value = true
  try {
    // 获取所有数据用于前端过滤
    const response = await api.get(`/api/hitokoto/list?skip=0&limit=10000`)
    allHitokotoList.value = response.data
    applyFilters()
  } catch (error) {
    ElMessage.error('加载一言列表失败')
  } finally {
    loading.value = false
  }
}

// 应用过滤和搜索
const applyFilters = () => {
  let filteredList = [...allHitokotoList.value]

  // 搜索过滤
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase()
    filteredList = filteredList.filter(item =>
      item.hitokoto.toLowerCase().includes(searchLower) ||
      (item.from_text && item.from_text.toLowerCase().includes(searchLower)) ||
      (item.from_who && item.from_who.toLowerCase().includes(searchLower))
    )
  }

  // 类型过滤
  if (filterType.value) {
    filteredList = filteredList.filter(item => item.type === filterType.value)
  }

  // 添加者过滤
  if (filterCreator.value) {
    const creatorLower = filterCreator.value.toLowerCase()
    filteredList = filteredList.filter(item =>
      item.creator && item.creator.toLowerCase().includes(creatorLower)
    )
  }

  // 最小长度过滤
  if (filterMinLength.value) {
    filteredList = filteredList.filter(item => item.length >= filterMinLength.value)
  }

  // 更新总数和分页
  total.value = filteredList.length

  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  hitokotoList.value = filteredList.slice(start, end)
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1 // 重置到第一页
  applyFilters()
}

// 过滤处理
const handleFilter = () => {
  currentPage.value = 1 // 重置到第一页
  applyFilters()
}

// 重置过滤器
const resetFilters = () => {
  searchText.value = ''
  filterType.value = ''
  filterCreator.value = ''
  filterMinLength.value = null
  currentPage.value = 1
  applyFilters()
}

// 加载统计信息
const loadStats = async () => {
  try {
    const response = await api.get('/api/hitokoto/stats')
    stats.value = response.data
  } catch (error) {
    ElMessage.error('加载统计信息失败')
  }
}

// 查看详情
const viewDetail = (hitokoto: any) => {
  selectedHitokoto.value = hitokoto
  showDetailDialog.value = true
}

// 删除一言
const deleteHitokoto = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条一言记录吗？', '确认删除', {
      type: 'warning'
    })
    
    await api.delete(`/api/hitokoto/${id}`)
    ElMessage.success('删除成功')
    loadHitokotoList()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  applyFilters()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  applyFilters()
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  loadHitokotoList()
  loadStats()
})
</script>

<style scoped>
.hitokoto-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 搜索过滤区域样式 */
.search-filters {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stats-section {
  margin-bottom: 20px;
}

.type-distribution h4 {
  margin-bottom: 10px;
  color: #333;
}

.hitokoto-content {
  line-height: 1.5;
  max-width: 300px;
  word-break: break-word;
}

.from-info {
  font-size: 12px;
}

.from-who {
  color: #666;
  margin-top: 2px;
}

.text-muted {
  color: #999;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.detail-content {
  max-height: 500px;
  overflow-y: auto;
}
</style>
