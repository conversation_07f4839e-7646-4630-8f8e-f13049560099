import logging
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from .database import engine, Base
from .routers import auth, tasks, share, accounts, notices, settings, hitokoto, users, profile
from .task_scheduler import init_task_scheduler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(title="LinuxDo ADT API", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],  # Vue开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(auth.router)
app.include_router(tasks.router)
app.include_router(share.router)
app.include_router(accounts.router)
app.include_router(notices.router)
app.include_router(settings.router)
app.include_router(hitokoto.router)
app.include_router(users.router)
app.include_router(profile.router)


@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    init_task_scheduler()


@app.get("/")
def read_root():
    return {"message": "LinuxDo ADT API is running"}
