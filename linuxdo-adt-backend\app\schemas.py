from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime


# 用户相关
class UserBase(BaseModel):
    username: str
    email: EmailStr


class UserCreate(UserBase):
    password: str


class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    role: Optional[str] = None
    is_active: Optional[bool] = None


class UserResponse(UserBase):
    id: int
    role: str
    is_active: bool
    email_verified: bool
    email_verified_at: Optional[datetime] = None
    created_at: datetime

    class Config:
        from_attributes = True


# 管理员创建用户
class AdminUserCreate(BaseModel):
    username: str
    email: EmailStr
    password: str
    role: str = "user"
    is_active: bool = True


# 个人中心相关
class ChangePasswordRequest(BaseModel):
    current_password: str
    new_password: str


class SendVerificationEmailRequest(BaseModel):
    pass  # 不需要额外参数，使用当前用户的邮箱


class ProfileUpdateRequest(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None


# 认证相关
class Token(BaseModel):
    access_token: str
    token_type: str


class TokenData(BaseModel):
    username: Optional[str] = None


# 任务相关
class TaskBase(BaseModel):
    title: str
    description: Optional[str] = None
    task_type: str
    duration_days: int


class TaskCreate(TaskBase):
    pass


class TaskUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    task_type: Optional[str] = None
    duration_days: Optional[int] = None
    status: Optional[str] = None


class TaskResponse(TaskBase):
    id: int
    status: str
    share_token: str
    creator_id: int
    creator_username: Optional[str] = None  # 创建者用户名
    created_at: datetime
    updated_at: Optional[datetime]
    expires_at: Optional[datetime]

    class Config:
        from_attributes = True


# 分享任务信息（不包含敏感信息）
class SharedTaskInfo(BaseModel):
    id: int
    title: str
    description: Optional[str]
    task_type: str
    duration_days: int
    created_at: datetime
    is_submitted: bool = False  # 是否已经有人提交过账号信息

    class Config:
        from_attributes = True


# 账号提交相关
class AccountSubmissionCreate(BaseModel):
    username: str
    password: str
    email: EmailStr
    level_info: str = ""
    key_info: str = ""
    need_notification: bool = True


# 管理员手动添加账号
class AdminAccountCreate(BaseModel):
    username: str
    password: str
    email: EmailStr
    level_info: str = ""
    key_info: str = ""
    need_notification: bool = True
    task_type: str
    duration_days: int
    description: Optional[str] = None


class AccountSubmissionResponse(BaseModel):
    id: int
    task_id: int
    username: str
    password: str  # 添加密码字段用于管理页面显示
    email: str
    level_info: str
    key_info: str
    need_notification: bool
    submitted_at: datetime
    status: str
    # 任务相关信息
    task_title: Optional[str] = None
    task_type: Optional[str] = None
    task_creator_username: Optional[str] = None

    class Config:
        from_attributes = True


# 系统提示信息
class SystemNoticeBase(BaseModel):
    title: str
    content: str
    priority: str = "info"  # info, warning, error, success
    is_active: bool = True


class SystemNoticeCreate(SystemNoticeBase):
    pass


class SystemNoticeResponse(SystemNoticeBase):
    id: int
    creator_id: int
    creator_username: Optional[str] = None  # 创建者用户名
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# 系统设置相关
class SystemSettingsBase(BaseModel):
    key: str
    value: str
    description: Optional[str] = None


class SystemSettingsCreate(SystemSettingsBase):
    pass


class SystemSettingsUpdate(BaseModel):
    value: str
    description: Optional[str] = None


class SystemSettingsResponse(SystemSettingsBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


# 邮件模板相关
class EmailTemplateBase(BaseModel):
    name: str
    subject: str
    content: str
    description: Optional[str] = None
    is_active: bool = True


class EmailTemplateCreate(EmailTemplateBase):
    pass


class EmailTemplateUpdate(BaseModel):
    subject: Optional[str] = None
    content: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class EmailTemplateResponse(EmailTemplateBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


# 邮件日志相关
class EmailLogResponse(BaseModel):
    id: int
    to_email: str
    subject: str
    content: str
    template_name: Optional[str]
    status: str
    error_message: Optional[str]
    sent_at: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True


# 邮件发送请求
class EmailSendRequest(BaseModel):
    to_email: str
    subject: str
    content: str
    template_name: Optional[str] = None


# 一言相关
class HitokotoCreate(BaseModel):
    hitokoto_id: Optional[int] = None
    uuid: str
    hitokoto: str
    type: Optional[str] = None
    from_text: Optional[str] = None
    from_who: Optional[str] = None
    creator: Optional[str] = None
    creator_uid: Optional[int] = None
    reviewer: Optional[int] = None
    commit_from: Optional[str] = None
    hitokoto_created_at: Optional[str] = None
    length: Optional[int] = None


class HitokotoResponse(BaseModel):
    id: int
    hitokoto_id: Optional[int]
    uuid: str
    hitokoto: str
    type: Optional[str]
    from_text: Optional[str]
    from_who: Optional[str]
    creator: Optional[str]
    creator_uid: Optional[int]
    reviewer: Optional[int]
    commit_from: Optional[str]
    hitokoto_created_at: Optional[str]
    length: Optional[int]
    created_at: datetime

    class Config:
        from_attributes = True
