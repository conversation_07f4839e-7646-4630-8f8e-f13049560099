<template>
  <div class="home-page">
    <div class="hero-section">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">LinuxDo ADT</h1>
          <h2 class="hero-subtitle">LinuxDo 自动代挂服务</h2>
          <p class="hero-description">
            专业的 LinuxDo 论坛自动代挂服务，帮助您保持账号活跃度，无需手动操作。
          </p>
          <div class="hero-buttons">
            <el-button type="primary" size="large" @click="goToLogin">
              <el-icon><User /></el-icon>
              管理后台
            </el-button>
            <el-button size="large" @click="scrollToFeatures">
              <el-icon><InfoFilled /></el-icon>
              了解更多
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="features-section" ref="featuresSection">
      <div class="container">
        <h2 class="section-title">服务特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon size="40"><Clock /></el-icon>
            </div>
            <h3>自动化操作</h3>
            <p>24小时自动代挂，无需手动操作，保持账号活跃状态</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon size="40"><Shield /></el-icon>
            </div>
            <h3>安全可靠</h3>
            <p>采用安全的自动化技术，保护您的账号信息安全</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon size="40"><Setting /></el-icon>
            </div>
            <h3>灵活配置</h3>
            <p>支持自定义代挂时长和频率，满足不同需求</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon size="40"><Bell /></el-icon>
            </div>
            <h3>实时通知</h3>
            <p>代挂状态实时通知，让您随时了解服务进度</p>
          </div>
        </div>
      </div>
    </div>

    <div class="how-it-works-section">
      <div class="container">
        <h2 class="section-title">使用流程</h2>
        <div class="steps-container">
          <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3>提交信息</h3>
              <p>通过分享链接提交您的 LinuxDo 账号信息</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3>开始代挂</h3>
              <p>我们的系统将自动开始为您的账号进行代挂服务</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3>保持活跃</h3>
              <p>您的账号将保持活跃状态，无需担心被冻结</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="contact-section">
      <div class="container">
        <h2 class="section-title">联系我们</h2>
        <div class="contact-content">
          <p>如果您有任何问题或需要帮助，请随时联系我们。</p>
          <div class="contact-info">
            <div class="contact-item">
              <el-icon><Message /></el-icon>
              <span>通过 LinuxDo 论坛私信联系</span>
            </div>
            <div class="contact-item">
              <el-icon><QuestionFilled /></el-icon>
              <span>查看常见问题解答</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="footer">
      <div class="container">
        <p>&copy; 2024 LinuxDo ADT. 专业的自动代挂服务。</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const featuresSection = ref<HTMLElement>()

const goToLogin = () => {
  router.push('/admin')
}

const scrollToFeatures = () => {
  if (featuresSection.value) {
    featuresSection.value.scrollIntoView({ behavior: 'smooth' })
  }
}
</script>

<style scoped>
.home-page {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 100px 0;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.hero-description {
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  line-height: 1.6;
  opacity: 0.8;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Features Section */
.features-section {
  padding: 80px 0;
  background-color: #f8f9fa;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #333;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  color: #409eff;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* How it works Section */
.how-it-works-section {
  padding: 80px 0;
  background: white;
}

.steps-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  flex-wrap: wrap;
}

.step {
  flex: 1;
  min-width: 250px;
  text-align: center;
  position: relative;
}

.step-number {
  width: 60px;
  height: 60px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 1rem;
}

.step-content h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.step-content p {
  color: #666;
  line-height: 1.6;
}

/* Contact Section */
.contact-section {
  padding: 80px 0;
  background-color: #f8f9fa;
}

.contact-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.contact-content p {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 2rem;
}

.contact-info {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #409eff;
  font-weight: 500;
}

/* Footer */
.footer {
  background-color: #333;
  color: white;
  padding: 2rem 0;
  text-align: center;
}

.footer p {
  margin: 0;
  opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.5rem;
  }
  
  .hero-description {
    font-size: 1rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .steps-container {
    flex-direction: column;
  }
  
  .contact-info {
    flex-direction: column;
    align-items: center;
  }
}
</style>
