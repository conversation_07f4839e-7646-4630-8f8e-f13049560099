import { createRouter, createWebHistory } from 'vue-router'
import DynamicHome from '../views/DynamicHome.vue'
import Login from '../views/Login.vue'
import Dashboard from '../views/Dashboard.vue'
import TaskList from '../views/TaskList.vue'
import AccountList from '../views/AccountList.vue'
import NoticeList from '../views/NoticeList.vue'
import SharePage from '../views/SharePage.vue'
import SystemSettings from '../views/SystemSettings.vue'
import HitokotoList from '../views/HitokotoList.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: DynamicHome
  },
  {
    path: '/admin/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/admin',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/admin/tasks'
      },
      {
        path: '/admin/tasks',
        name: 'TaskList',
        component: TaskList
      },
      {
        path: '/admin/accounts',
        name: 'AccountList',
        component: AccountList
      },
      {
        path: '/admin/notices',
        name: 'NoticeList',
        component: NoticeList,
        meta: { requiresAdmin: true }
      },
      {
        path: '/admin/settings',
        name: 'SystemSettings',
        component: SystemSettings,
        meta: { requiresAdmin: true }
      },
      {
        path: '/admin/hitokoto',
        name: 'HitokotoList',
        component: HitokotoList,
        meta: { requiresAdmin: true }
      }
    ]
  },
  {
    path: '/share/:shareToken',
    name: 'SharePage',
    component: SharePage
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('access_token')

  if (to.meta.requiresAuth && !token) {
    next('/admin/login')
  } else if (to.path === '/admin/login' && token) {
    next('/admin')
  } else {
    next()
  }
})

export default router
