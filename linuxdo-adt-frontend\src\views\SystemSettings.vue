<template>
  <div class="system-settings">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>系统设置</span>
        </div>
      </template>

      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础设置 -->
        <el-tab-pane label="基础设置" name="basic">
          <el-form :model="basicSettings" label-width="120px">
            <el-form-item label="允许注册">
              <el-switch
                v-model="basicSettings.registration_enabled"
                @change="markSettingsChanged"
              />
              <div class="setting-desc">关闭后新用户无法注册账号</div>
            </el-form-item>

            <el-form-item label="网站名称">
              <el-input
                v-model="basicSettings.site_name"
                @input="markSettingsChanged"
                placeholder="请输入网站名称"
              />
            </el-form-item>

            <el-form-item label="网站URL">
              <el-input
                v-model="basicSettings.site_url"
                @input="markSettingsChanged"
                placeholder="请输入网站URL"
              />
            </el-form-item>

            <el-form-item label="显示首页">
              <el-switch
                v-model="basicSettings.show_homepage"
                @change="markSettingsChanged"
              />
              <div class="setting-desc">关闭后首页将显示一言页面，开启后显示项目介绍页面</div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="saveBasicSettings"
                :disabled="!basicSettingsChanged"
                :loading="savingBasic"
              >
                保存基础设置
              </el-button>
              <el-button @click="resetBasicSettings" :disabled="!basicSettingsChanged">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 邮件设置 -->
        <el-tab-pane label="邮件设置" name="email">
          <el-form :model="emailSettings" label-width="120px">
            <el-form-item label="SMTP服务器">
              <el-input
                v-model="emailSettings.smtp_server"
                @input="markEmailSettingsChanged"
                placeholder="如: smtp.qq.com"
              />
            </el-form-item>

            <el-form-item label="SMTP端口">
              <el-input
                v-model="emailSettings.smtp_port"
                @input="markEmailSettingsChanged"
                placeholder="如: 587"
                type="number"
              />
            </el-form-item>

            <el-form-item label="SMTP用户名">
              <el-input
                v-model="emailSettings.smtp_username"
                @input="markEmailSettingsChanged"
                placeholder="邮箱账号"
              />
            </el-form-item>

            <el-form-item label="SMTP密码">
              <el-input
                v-model="emailSettings.smtp_password"
                @input="markEmailSettingsChanged"
                placeholder="邮箱密码或授权码"
                type="password"
                show-password
              />
            </el-form-item>

            <el-form-item label="使用TLS">
              <el-switch
                v-model="emailSettings.smtp_use_tls"
                @change="markEmailSettingsChanged"
              />
            </el-form-item>

            <el-form-item label="发件人邮箱">
              <el-input
                v-model="emailSettings.from_email"
                @input="markEmailSettingsChanged"
                placeholder="发件人邮箱地址"
              />
            </el-form-item>

            <el-form-item label="发件人名称">
              <el-input
                v-model="emailSettings.from_name"
                @input="markEmailSettingsChanged"
                placeholder="发件人显示名称"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="saveEmailSettings"
                :disabled="!emailSettingsChanged"
                :loading="savingEmail"
              >
                保存邮件设置
              </el-button>
              <el-button @click="resetEmailSettings" :disabled="!emailSettingsChanged">
                重置
              </el-button>
              <el-button type="success" @click="sendTestEmail" style="margin-left: 10px;">
                发送测试邮件
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 邮件模板 -->
        <el-tab-pane label="邮件模板" name="templates">
          <div style="margin-bottom: 20px; display: flex; gap: 10px;">
            <el-button type="primary" @click="showTemplateDialog = true">
              新增模板
            </el-button>
            <el-input
              v-model="templateSearchText"
              placeholder="搜索模板名称或主题"
              clearable
              style="width: 300px"
              @input="filterTemplates"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <el-table :data="emailTemplates" style="width: 100%">
            <el-table-column label="序号" width="80" type="index" :index="(index) => index + 1" />
            <el-table-column prop="name" label="模板名称" width="200" />
            <el-table-column prop="subject" label="邮件主题" min-width="200" show-overflow-tooltip />
            <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
            <el-table-column prop="is_active" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                  {{ scope.row.is_active ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="scope">
                <el-button size="small" @click="editTemplate(scope.row)">编辑</el-button>
                <el-button size="small" type="danger" @click="deleteTemplate(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 邮件事件 -->
        <el-tab-pane label="邮件事件" name="events">
          <div style="margin-bottom: 20px;">
            <el-alert
              title="邮件事件说明"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                配置系统事件触发时自动发送的邮件模板。当对应事件发生时，系统会自动使用配置的模板发送邮件。
              </template>
            </el-alert>
          </div>

          <el-table :data="emailEvents" style="width: 100%">
            <el-table-column label="序号" width="80" type="index" :index="(index) => index + 1" />
            <el-table-column prop="key" label="事件标识" width="200" />
            <el-table-column prop="name" label="事件名称" width="150" />
            <el-table-column prop="description" label="事件描述" min-width="200" />
            <el-table-column prop="template_name" label="关联模板" width="180">
              <template #default="scope">
                <el-tag v-if="scope.row.template_name" type="success">
                  {{ scope.row.template_name }}
                </el-tag>
                <el-tag v-else type="info">未配置</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  @click="openEventTemplateDialog(scope.row)"
                >
                  {{ scope.row.template_name ? '修改模板' : '配置模板' }}
                </el-button>
                <el-button
                  v-if="scope.row.template_name"
                  type="danger"
                  size="small"
                  @click="removeEventTemplate(scope.row.key)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 邮件日志 -->
        <el-tab-pane label="邮件日志" name="logs">
          <div style="margin-bottom: 20px;">
            <el-input
              v-model="logSearchText"
              placeholder="搜索收件人或主题"
              clearable
              style="width: 300px"
              @input="filterLogs"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <el-table :data="filteredEmailLogs" style="width: 100%">
            <el-table-column label="序号" width="80" type="index" :index="(index) => index + 1" />
            <el-table-column prop="to_email" label="收件人" width="200" />
            <el-table-column prop="subject" label="主题" min-width="200" show-overflow-tooltip />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="发送时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="error_message" label="错误信息" min-width="200" show-overflow-tooltip />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 邮件模板编辑对话框 -->
    <el-dialog
      v-model="showTemplateDialog"
      :title="editingTemplate ? '编辑邮件模板' : '新增邮件模板'"
      width="80%"
    >
      <el-row :gutter="20">
        <!-- 左侧表单 -->
        <el-col :span="16">
          <el-form :model="templateForm" label-width="100px">
            <el-form-item label="模板名称" required>
              <el-input v-model="templateForm.name" :disabled="editingTemplate" />
            </el-form-item>
            <el-form-item label="邮件主题" required>
              <el-input v-model="templateForm.subject" placeholder="支持模板变量，如 {{ site_name }}" />
            </el-form-item>
            <el-form-item label="邮件内容" required>
              <el-input
                v-model="templateForm.content"
                type="textarea"
                :rows="12"
                placeholder="支持HTML格式和模板变量"
              />
            </el-form-item>
            <el-form-item label="描述">
              <el-input v-model="templateForm.description" />
            </el-form-item>
            <el-form-item label="启用状态">
              <el-switch v-model="templateForm.is_active" />
            </el-form-item>
          </el-form>
        </el-col>

        <!-- 右侧变量列表 -->
        <el-col :span="8">
          <div class="template-variables">
            <h4 style="margin-top: 0; color: #2c3e50;">📝 支持的模板变量</h4>

            <el-collapse v-model="activeVariableGroups" accordion>
              <!-- 通用变量 -->
              <el-collapse-item title="🌐 通用变量" name="common">
                <div class="variable-group">
                  <div class="variable-item" @click="insertVariable('{{ site_name }}')">
                    <code>{{ site_name }}</code>
                    <span class="variable-desc">网站名称</span>
                  </div>
                  <div class="variable-item" @click="insertVariable('{{ login_url }}')">
                    <code>{{ login_url }}</code>
                    <span class="variable-desc">登录链接</span>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 用户相关变量 -->
              <el-collapse-item title="👤 用户变量" name="user">
                <div class="variable-group">
                  <div class="variable-item" @click="insertVariable('{{ username }}')">
                    <code>{{ username }}</code>
                    <span class="variable-desc">用户名</span>
                  </div>
                  <div class="variable-item" @click="insertVariable('{{ user_email }}')">
                    <code>{{ user_email }}</code>
                    <span class="variable-desc">用户邮箱</span>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 任务相关变量 -->
              <el-collapse-item title="📋 任务变量" name="task">
                <div class="variable-group">
                  <div class="variable-item" @click="insertVariable('{{ task_title }}')">
                    <code>{{ task_title }}</code>
                    <span class="variable-desc">任务标题</span>
                  </div>
                  <div class="variable-item" @click="insertVariable('{{ task_type }}')">
                    <code>{{ task_type }}</code>
                    <span class="variable-desc">任务类型</span>
                  </div>
                  <div class="variable-item" @click="insertVariable('{{ task_status }}')">
                    <code>{{ task_status }}</code>
                    <span class="variable-desc">任务状态</span>
                  </div>
                  <div class="variable-item" @click="insertVariable('{{ duration_days }}')">
                    <code>{{ duration_days }}</code>
                    <span class="variable-desc">代挂天数</span>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 账号相关变量 -->
              <el-collapse-item title="🔐 账号变量" name="account">
                <div class="variable-group">
                  <div class="variable-item" @click="insertVariable('{{ account_username }}')">
                    <code>{{ account_username }}</code>
                    <span class="variable-desc">LinuxDo用户名</span>
                  </div>
                  <div class="variable-item" @click="insertVariable('{{ account_email }}')">
                    <code>{{ account_email }}</code>
                    <span class="variable-desc">LinuxDo邮箱</span>
                  </div>
                  <div class="variable-item" @click="insertVariable('{{ level_info }}')">
                    <code>{{ level_info }}</code>
                    <span class="variable-desc">等级信息</span>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 密码重置变量 -->
              <el-collapse-item title="🔑 密码重置" name="reset">
                <div class="variable-group">
                  <div class="variable-item" @click="insertVariable('{{ reset_url }}')">
                    <code>{{ reset_url }}</code>
                    <span class="variable-desc">重置链接</span>
                  </div>
                  <div class="variable-item" @click="insertVariable('{{ reset_token }}')">
                    <code>{{ reset_token }}</code>
                    <span class="variable-desc">重置令牌</span>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 邮箱验证变量 -->
              <el-collapse-item title="📧 邮箱验证" name="verification">
                <div class="variable-group">
                  <div class="variable-item" @click="insertVariable('{{ email_to_verify }}')">
                    <code>{{ email_to_verify }}</code>
                    <span class="variable-desc">要验证的邮箱</span>
                  </div>
                  <div class="variable-item" @click="insertVariable('{{ verification_url }}')">
                    <code>{{ verification_url }}</code>
                    <span class="variable-desc">验证链接</span>
                  </div>
                  <div class="variable-item" @click="insertVariable('{{ verification_token }}')">
                    <code>{{ verification_token }}</code>
                    <span class="variable-desc">验证令牌</span>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>

            <div class="variable-tips">
              <el-alert
                title="使用提示"
                type="info"
                :closable="false"
                show-icon
              >
                <template #default>
                  <ul style="margin: 0; padding-left: 20px;">
                    <li>点击变量名可快速插入</li>
                    <li>变量使用双花括号包围</li>
                    <li>支持HTML格式内容</li>
                    <li>未定义的变量会显示为空</li>
                  </ul>
                </template>
              </el-alert>
            </div>
          </div>
        </el-col>
      </el-row>

      <template #footer>
        <el-button @click="showTemplateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveTemplate">保存</el-button>
      </template>
    </el-dialog>

    <!-- 测试邮件对话框 -->
    <el-dialog v-model="showTestEmailDialog" title="发送测试邮件" width="40%">
      <el-form :model="testEmailForm" label-width="100px">
        <el-form-item label="收件人" required>
          <el-input v-model="testEmailForm.to_email" placeholder="请输入收件人邮箱" />
        </el-form-item>
        <el-form-item label="邮件主题" required>
          <el-input v-model="testEmailForm.subject" />
        </el-form-item>
        <el-form-item label="邮件内容" required>
          <el-input
            v-model="testEmailForm.content"
            type="textarea"
            :rows="5"
            placeholder="测试邮件内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showTestEmailDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmSendTestEmail">发送</el-button>
      </template>
    </el-dialog>

    <!-- 邮件事件模板配置对话框 -->
    <el-dialog
      v-model="showEventTemplateDialog"
      :title="`配置事件模板 - ${currentEvent?.name}`"
      width="50%"
    >
      <el-form :model="eventTemplateForm" label-width="120px">
        <el-form-item label="事件名称">
          <el-input v-model="currentEvent.name" disabled />
        </el-form-item>
        <el-form-item label="事件描述">
          <el-input v-model="currentEvent.description" type="textarea" :rows="2" disabled />
        </el-form-item>
        <el-form-item label="选择模板" required>
          <el-select
            v-model="eventTemplateForm.template_name"
            placeholder="请选择邮件模板"
            style="width: 100%"
            @change="updateTemplatePreview"
          >
            <el-option
              v-for="template in emailTemplates"
              :key="template.name"
              :label="`${template.name} - ${template.subject}`"
              :value="template.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模板预览" v-if="selectedTemplatePreview">
          <div class="template-preview">
            <div class="preview-item">
              <strong>主题：</strong>{{ selectedTemplatePreview.subject }}
            </div>
            <div class="preview-item">
              <strong>描述：</strong>{{ selectedTemplatePreview.description }}
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEventTemplateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveEventTemplate" :loading="savingEventTemplate">
          保存配置
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '../api/index'

const activeTab = ref('basic')
const showTemplateDialog = ref(false)
const showTestEmailDialog = ref(false)
const showEventTemplateDialog = ref(false)
const editingTemplate = ref(false)

// 保存状态
const basicSettingsChanged = ref(false)
const emailSettingsChanged = ref(false)
const savingBasic = ref(false)
const savingEmail = ref(false)
const savingEventTemplate = ref(false)

// 原始设置备份
const originalBasicSettings = ref({})
const originalEmailSettings = ref({})

// 基础设置
const basicSettings = reactive({
  registration_enabled: true,
  site_name: '',
  site_url: '',
  show_homepage: true
})

// 邮件设置
const emailSettings = reactive({
  smtp_server: '',
  smtp_port: '',
  smtp_username: '',
  smtp_password: '',
  smtp_use_tls: true,
  from_email: '',
  from_name: ''
})

// 邮件模板
const emailTemplates = ref([])
const allEmailTemplates = ref([]) // 存储所有模板数据
const templateForm = reactive({
  name: '',
  subject: '',
  content: '',
  description: '',
  is_active: true
})

// 邮件日志
const emailLogs = ref([])
const allEmailLogs = ref([]) // 存储所有日志数据
const filteredEmailLogs = ref([]) // 过滤后的日志数据

// 搜索相关
const templateSearchText = ref('')
const logSearchText = ref('')

// 模板变量相关
const activeVariableGroups = ref(['common']) // 默认展开通用变量

// 邮件事件相关
const emailEvents = ref([])
const currentEvent = ref(null)
const selectedTemplatePreview = ref(null)
const eventTemplateForm = reactive({
  template_name: ''
})

// 测试邮件表单
const testEmailForm = reactive({
  to_email: '',
  subject: '测试邮件',
  content: '这是一封测试邮件，用于验证邮件配置是否正确。'
})

// 获取系统设置
const loadSystemSettings = async () => {
  try {
    const response = await api.get('/api/settings/system')
    const settings = response.data

    // 确保settings是数组
    if (Array.isArray(settings)) {
      settings.forEach(setting => {
        if (setting.key in basicSettings) {
          basicSettings[setting.key] = (setting.key === 'registration_enabled' || setting.key === 'show_homepage')
            ? setting.value === 'true'
            : setting.value
        }
        if (setting.key in emailSettings) {
          emailSettings[setting.key] = setting.key === 'smtp_use_tls'
            ? setting.value === 'true'
            : setting.value
        }
      })
    } else {
      console.warn('Settings data is not an array:', settings)
    }

    // 保存原始设置
    originalBasicSettings.value = { ...basicSettings }
    originalEmailSettings.value = { ...emailSettings }

    // 重置变更状态
    basicSettingsChanged.value = false
    emailSettingsChanged.value = false
  } catch (error) {
    console.error('Load settings error:', error)
    ElMessage.error('加载系统设置失败')
  }
}

// 标记基础设置已变更
const markSettingsChanged = () => {
  basicSettingsChanged.value = true
}

// 标记邮件设置已变更
const markEmailSettingsChanged = () => {
  emailSettingsChanged.value = true
}

// 保存基础设置
const saveBasicSettings = async () => {
  savingBasic.value = true
  try {
    const settingsToSave = {}

    // 只保存变更的设置
    Object.keys(basicSettings).forEach(key => {
      const currentValue = typeof basicSettings[key] === 'boolean'
        ? basicSettings[key].toString()
        : (basicSettings[key] || '').toString()
      const originalValue = typeof originalBasicSettings.value[key] === 'boolean'
        ? originalBasicSettings.value[key].toString()
        : (originalBasicSettings.value[key] || '').toString()

      if (currentValue !== originalValue) {
        settingsToSave[key] = currentValue
      }
    })

    console.log('Settings to save:', settingsToSave) // 调试日志

    if (Object.keys(settingsToSave).length > 0) {
      await api.put('/api/settings/system/batch', settingsToSave)
      ElMessage.success('基础设置保存成功')

      // 更新原始设置
      originalBasicSettings.value = { ...basicSettings }
      basicSettingsChanged.value = false
    } else {
      ElMessage.info('没有设置需要保存')
    }
  } catch (error) {
    console.error('Save settings error:', error) // 调试日志
    ElMessage.error('保存基础设置失败')
  } finally {
    savingBasic.value = false
  }
}

// 重置基础设置
const resetBasicSettings = () => {
  Object.assign(basicSettings, originalBasicSettings.value)
  basicSettingsChanged.value = false
}

// 保存邮件设置
const saveEmailSettings = async () => {
  savingEmail.value = true
  try {
    const settingsToSave = {}

    // 只保存变更的设置
    Object.keys(emailSettings).forEach(key => {
      const currentValue = typeof emailSettings[key] === 'boolean'
        ? emailSettings[key].toString()
        : emailSettings[key]
      const originalValue = typeof originalEmailSettings.value[key] === 'boolean'
        ? originalEmailSettings.value[key].toString()
        : originalEmailSettings.value[key]

      if (currentValue !== originalValue) {
        settingsToSave[key] = currentValue
      }
    })

    if (Object.keys(settingsToSave).length > 0) {
      await api.put('/api/settings/system/batch', settingsToSave)
      ElMessage.success('邮件设置保存成功')

      // 更新原始设置
      originalEmailSettings.value = { ...emailSettings }
      emailSettingsChanged.value = false
    } else {
      ElMessage.info('没有设置需要保存')
    }
  } catch (error) {
    ElMessage.error('保存邮件设置失败')
  } finally {
    savingEmail.value = false
  }
}

// 重置邮件设置
const resetEmailSettings = () => {
  Object.assign(emailSettings, originalEmailSettings.value)
  emailSettingsChanged.value = false
}



// 获取邮件模板
const loadEmailTemplates = async () => {
  try {
    const response = await api.get('/api/settings/email-templates')
    allEmailTemplates.value = response.data
    filterTemplates()
  } catch (error) {
    ElMessage.error('加载邮件模板失败')
  }
}

// 获取邮件日志
const loadEmailLogs = async () => {
  try {
    const response = await api.get('/api/settings/email-logs')
    allEmailLogs.value = response.data
    filterLogs()
  } catch (error) {
    ElMessage.error('加载邮件日志失败')
  }
}

// 过滤邮件模板
const filterTemplates = () => {
  if (!templateSearchText.value) {
    emailTemplates.value = allEmailTemplates.value
  } else {
    const searchLower = templateSearchText.value.toLowerCase()
    emailTemplates.value = allEmailTemplates.value.filter(template =>
      template.name.toLowerCase().includes(searchLower) ||
      template.subject.toLowerCase().includes(searchLower) ||
      (template.description && template.description.toLowerCase().includes(searchLower))
    )
  }
}

// 过滤邮件日志
const filterLogs = () => {
  if (!logSearchText.value) {
    filteredEmailLogs.value = allEmailLogs.value
  } else {
    const searchLower = logSearchText.value.toLowerCase()
    filteredEmailLogs.value = allEmailLogs.value.filter(log =>
      log.to_email.toLowerCase().includes(searchLower) ||
      log.subject.toLowerCase().includes(searchLower)
    )
  }
}

// 插入模板变量
const insertVariable = (variable) => {
  // 获取当前光标位置并插入变量
  const textarea = document.querySelector('textarea[placeholder*="支持HTML格式和模板变量"]')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const currentContent = templateForm.content

    // 在光标位置插入变量
    templateForm.content = currentContent.substring(0, start) + variable + currentContent.substring(end)

    // 设置新的光标位置
    nextTick(() => {
      textarea.focus()
      textarea.setSelectionRange(start + variable.length, start + variable.length)
    })
  } else {
    // 如果找不到textarea，直接追加到内容末尾
    templateForm.content += variable
  }
}

// 邮件事件管理
const loadEmailEvents = async () => {
  try {
    const response = await api.get('/api/settings/email-events')
    const { events, available_events } = response.data

    // 合并事件信息和模板映射
    emailEvents.value = available_events.map(event => ({
      ...event,
      template_name: events[event.key] || null
    }))
  } catch (error) {
    ElMessage.error('加载邮件事件失败')
  }
}

const openEventTemplateDialog = (event) => {
  currentEvent.value = { ...event }
  eventTemplateForm.template_name = event.template_name || ''
  selectedTemplatePreview.value = null
  updateTemplatePreview()
  showEventTemplateDialog.value = true
}

const updateTemplatePreview = () => {
  if (eventTemplateForm.template_name) {
    selectedTemplatePreview.value = emailTemplates.value.find(
      template => template.name === eventTemplateForm.template_name
    )
  } else {
    selectedTemplatePreview.value = null
  }
}

const saveEventTemplate = async () => {
  if (!eventTemplateForm.template_name) {
    ElMessage.error('请选择邮件模板')
    return
  }

  savingEventTemplate.value = true
  try {
    await api.post(`/api/settings/email-events/${currentEvent.value.key}?template_name=${eventTemplateForm.template_name}`)
    ElMessage.success('事件模板配置成功')
    showEventTemplateDialog.value = false
    loadEmailEvents()
  } catch (error) {
    ElMessage.error('配置事件模板失败')
  } finally {
    savingEventTemplate.value = false
  }
}

const removeEventTemplate = async (eventKey) => {
  try {
    await ElMessageBox.confirm('确定要移除这个事件的邮件模板配置吗？', '确认移除', {
      type: 'warning'
    })

    await api.delete(`/api/settings/email-events/${eventKey}`)
    ElMessage.success('事件模板配置已移除')
    loadEmailEvents()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除事件模板配置失败')
    }
  }
}

// 编辑模板
const editTemplate = (template) => {
  editingTemplate.value = true
  Object.assign(templateForm, template)
  showTemplateDialog.value = true
}

// 保存模板
const saveTemplate = async () => {
  try {
    if (editingTemplate.value) {
      await api.put(`/api/settings/email-templates/${templateForm.id}`, templateForm)
    } else {
      await api.post('/api/settings/email-templates', templateForm)
    }

    ElMessage.success('模板保存成功')
    showTemplateDialog.value = false
    resetTemplateForm()
    loadEmailTemplates()
  } catch (error) {
    ElMessage.error('保存模板失败')
  }
}

// 删除模板
const deleteTemplate = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这个邮件模板吗？', '确认删除', {
      type: 'warning'
    })

    await api.delete(`/api/settings/email-templates/${id}`)
    ElMessage.success('模板删除成功')
    loadEmailTemplates()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除模板失败')
    }
  }
}

// 重置模板表单
const resetTemplateForm = () => {
  Object.assign(templateForm, {
    name: '',
    subject: '',
    content: '',
    description: '',
    is_active: true
  })
  editingTemplate.value = false
}

// 发送测试邮件
const sendTestEmail = () => {
  showTestEmailDialog.value = true
}

const confirmSendTestEmail = async () => {
  try {
    await api.post('/api/settings/send-test-email', testEmailForm)
    ElMessage.success('测试邮件发送成功')
    showTestEmailDialog.value = false
  } catch (error) {
    ElMessage.error('测试邮件发送失败')
  }
}

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    'sent': 'success',
    'failed': 'danger',
    'pending': 'warning'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    'sent': '已发送',
    'failed': '发送失败',
    'pending': '待发送'
  }
  return texts[status] || status
}

onMounted(() => {
  loadSystemSettings()
  loadEmailTemplates()
  loadEmailLogs()
  loadEmailEvents()
})
</script>

<style scoped>
.system-settings {
  padding: 20px;
}

/* 模板变量样式 */
.template-variables {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  height: 500px;
  overflow-y: auto;
}

.template-variables h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.variable-group {
  margin-bottom: 8px;
}

.variable-item {
  display: flex;
  flex-direction: column;
  padding: 8px 12px;
  margin-bottom: 6px;
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.variable-item:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  transform: translateY(-1px);
}

.variable-item code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #e91e63;
  background: none;
  padding: 0;
  margin-bottom: 2px;
  font-weight: 600;
}

.variable-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.2;
}

.variable-tips {
  margin-top: 16px;
}

.variable-tips .el-alert {
  border-radius: 4px;
}

.variable-tips ul {
  font-size: 12px;
  line-height: 1.4;
}

.variable-tips li {
  margin-bottom: 4px;
}

/* 事件模板配置样式 */
.template-preview {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e1e8ed;
}

.preview-item {
  margin-bottom: 8px;
  line-height: 1.4;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.preview-item strong {
  color: #2c3e50;
  margin-right: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.setting-desc {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
