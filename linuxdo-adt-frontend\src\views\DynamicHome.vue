<template>
  <div v-if="loading" class="loading-container">
    <!-- 纯透明加载界面，无任何文字或图标 -->
  </div>
  <component v-else :is="currentComponent" />
</template>

<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent } from 'vue'
import axios from 'axios'

const loading = ref(true)
const showHomepage = ref(true)

// 动态导入组件
const Home = defineAsyncComponent(() => import('./Home.vue'))
const Hitokoto = defineAsyncComponent(() => import('./Hitokoto.vue'))

const currentComponent = ref(Home)

// 获取首页显示设置
const fetchHomepageSettings = async () => {
  try {
    const response = await axios.get('http://localhost:8000/api/settings/homepage-enabled')
    showHomepage.value = response.data.enabled
    
    // 根据设置选择组件
    currentComponent.value = showHomepage.value ? Home : Hitokoto
  } catch (error) {
    console.error('获取首页设置失败:', error)
    // 默认显示首页
    currentComponent.value = Home
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchHomepageSettings()
})
</script>

<style scoped>
.loading-container {
  width: 100vw;
  height: 100vh;
  background: transparent;
  /* 完全透明的加载界面，模拟浏览器原生加载 */
}
</style>
