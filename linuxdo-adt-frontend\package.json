{"name": "linuxdo-adt-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vue-tsc -b && vite build", "preview": "vite preview --host"}, "dependencies": {"vue": "^3.5.17", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "vue-router": "^4.2.5", "axios": "^1.6.2"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}