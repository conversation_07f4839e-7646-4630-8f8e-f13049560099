#!/usr/bin/env python3
"""
快速生成测试数据脚本 - 创建500个任务和500个账号
"""
import sys
import os
import secrets
import random
import string
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.models import User, Task, AccountSubmission, china_now

def generate_data():
    """快速生成数据"""
    print("开始生成测试数据...")
    
    db = SessionLocal()
    try:
        # 获取管理员用户
        admin = db.query(User).filter(User.role == "admin").first()
        if not admin:
            from app.auth import get_password_hash
            admin = User(
                username="admin",
                email="<EMAIL>",
                password_hash=get_password_hash("admin123"),
                role="admin"
            )
            db.add(admin)
            db.commit()
            db.refresh(admin)
            print("创建管理员用户: admin/admin123")
        
        # 清理现有数据
        db.query(AccountSubmission).delete()
        db.query(Task).delete()
        db.commit()
        print("清理现有数据完成")
        
        # 生成500个任务
        print("生成任务中...")
        tasks = []
        task_types = ["升2级", "升3级", "保号", "刷经验", "刷积分"]
        statuses = ["待提交", "进行中", "已完成", "已取消"]
        
        for i in range(500):
            task_type = random.choice(task_types)
            status = random.choice(statuses)
            duration = random.randint(1, 30)
            
            if status == "待提交":
                title = f"{task_type}任务{i+1}"
            else:
                username = f"user{random.randint(1000, 9999)}"
                title = f"{username}+{task_type}"
            
            # 设置过期时间
            if status in ["已完成", "已取消"]:
                expires_at = china_now() - timedelta(days=random.randint(1, 10))
            else:
                expires_at = china_now() + timedelta(days=duration + random.randint(1, 7))
            
            task = Task(
                title=title,
                description=f"这是第{i+1}个测试任务",
                task_type=task_type,
                duration_days=duration,
                status=status,
                share_token=secrets.token_urlsafe(32),
                creator_id=admin.id,
                expires_at=expires_at,
                created_at=china_now() - timedelta(days=random.randint(0, 30))
            )
            tasks.append(task)
            
            if (i + 1) % 100 == 0:
                print(f"已生成 {i + 1} 个任务")
        
        db.add_all(tasks)
        db.commit()
        print("任务创建完成")
        
        # 生成500个账号
        print("生成账号中...")
        accounts = []
        non_pending_tasks = [t for t in tasks if t.status != "待提交"]
        
        for i in range(min(500, len(non_pending_tasks))):
            task = non_pending_tasks[i]
            
            username = f"testuser{random.randint(10000, 99999)}"
            password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))
            email = f"{username}@test.com"
            
            # 根据任务状态设置账号状态
            if task.status == "已完成":
                account_status = "已完成"
            elif task.status == "已取消":
                account_status = "已取消"
            else:
                account_status = random.choice(["进行中", "已完成"])
            
            account = AccountSubmission(
                task_id=task.id,
                username=username,
                password=password,
                email=email,
                level_info=f"等级{random.randint(1, 10)}",
                key_info=f"key_{random.randint(10000, 99999)}" if random.choice([True, False]) else "",
                need_notification=random.choice([True, False]),
                status=account_status,
                submitted_at=task.created_at + timedelta(hours=random.randint(1, 24))
            )
            accounts.append(account)
            
            # 更新任务标题
            if not task.title.endswith(f"+{task.task_type}"):
                task.title = f"{username}+{task.task_type}"
            
            if (i + 1) % 100 == 0:
                print(f"已生成 {i + 1} 个账号")
        
        db.add_all(accounts)
        db.commit()
        
        print(f"\n数据生成完成！")
        print(f"任务总数: {len(tasks)}")
        print(f"账号总数: {len(accounts)}")
        
        # 统计信息
        print("\n任务状态统计:")
        for status in statuses:
            count = len([t for t in tasks if t.status == status])
            print(f"  {status}: {count}")
        
        print("\n账号状态统计:")
        account_statuses = ["进行中", "已完成", "已取消"]
        for status in account_statuses:
            count = len([a for a in accounts if a.status == status])
            print(f"  {status}: {count}")
        
    except Exception as e:
        print(f"生成数据时发生错误: {e}")
        import traceback
        traceback.print_exc()
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    generate_data()
