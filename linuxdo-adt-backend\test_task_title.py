#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试任务标题逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.database import get_db, engine
from app.models import Task, AccountSubmission

def test_task_titles():
    """测试任务标题显示逻辑"""
    print("测试任务标题逻辑...")
    
    with engine.connect() as conn:
        # 查询所有任务及其对应的账号信息
        result = conn.execute("""
            SELECT 
                t.id,
                t.title,
                t.task_type,
                t.status as task_status,
                a.username,
                a.status as account_status
            FROM tasks t
            LEFT JOIN account_submissions a ON t.id = a.task_id
            ORDER BY t.id
        """)
        
        tasks = result.fetchall()
        
        print(f"\n找到 {len(tasks)} 个任务记录:")
        print("-" * 80)
        
        for task in tasks:
            task_id, title, task_type, task_status, username, account_status = task
            
            print(f"任务ID: {task_id}")
            print(f"当前标题: {title}")
            print(f"任务类型: {task_type}")
            print(f"任务状态: {task_status}")
            
            if username:
                print(f"账号用户名: {username}")
                print(f"账号状态: {account_status}")
                expected_title = f"{username}+{task_type}"
                print(f"期望标题: {expected_title}")
                
                if title == expected_title:
                    print("✅ 标题正确")
                else:
                    print("❌ 标题需要更新")
            else:
                print("📝 没有账号提交")
                if len(title) == 36 and title.count('-') == 4:  # UUID格式检查
                    print("✅ UUID格式正确")
                else:
                    print("❌ 不是有效的UUID格式")
            
            print("-" * 80)

def fix_task_titles():
    """修复任务标题"""
    print("\n开始修复任务标题...")
    
    with engine.connect() as conn:
        # 更新有账号的任务标题
        result = conn.execute("""
            UPDATE tasks 
            SET title = (
                SELECT CONCAT(a.username, '+', tasks.task_type)
                FROM account_submissions a 
                WHERE a.task_id = tasks.id
                LIMIT 1
            )
            WHERE EXISTS (
                SELECT 1 FROM account_submissions a 
                WHERE a.task_id = tasks.id
            )
        """)
        
        updated_count = result.rowcount
        conn.commit()
        
        print(f"更新了 {updated_count} 个任务的标题")

if __name__ == "__main__":
    test_task_titles()
    
    # 询问是否要修复
    response = input("\n是否要修复不正确的任务标题? (y/N): ")
    if response.lower() == 'y':
        fix_task_titles()
        print("\n修复完成，重新测试:")
        test_task_titles()
