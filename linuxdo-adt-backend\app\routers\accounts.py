import secrets
from datetime import datetime, timedelta
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ..database import get_db
from ..models import User, AccountSubmission, Task
from ..schemas import AccountSubmissionResponse, AccountSubmissionCreate, AdminAccountCreate
from ..auth import get_current_active_user
from ..task_scheduler import check_and_update_tasks_by_account

router = APIRouter(prefix="/api/accounts", tags=["账号管理"])


def check_admin_permission(current_user: User = Depends(get_current_active_user)):
    """检查管理员权限"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


def generate_share_token() -> str:
    """生成分享令牌"""
    return secrets.token_urlsafe(32)


@router.get("/")
def get_account_submissions(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取账号提交列表（管理员可查看所有账号提交，普通用户只能查看自己任务的账号提交）"""
    # 构建查询条件，使用关联查询获取任务和创建者信息
    if current_user.role == "admin":
        # 管理员可以查看所有账号提交
        query = db.query(AccountSubmission).join(Task, AccountSubmission.task_id == Task.id).join(User, Task.creator_id == User.id)
        total = query.count()
        submissions = query.order_by(AccountSubmission.submitted_at.desc()).offset(skip).limit(limit).all()
    else:
        # 普通用户只能查看自己任务的账号提交
        query = db.query(AccountSubmission).join(Task, AccountSubmission.task_id == Task.id).join(User, Task.creator_id == User.id).filter(Task.creator_id == current_user.id)
        total = query.count()
        submissions = query.order_by(AccountSubmission.submitted_at.desc()).offset(skip).limit(limit).all()

    # 检查并更新相关任务状态
    for submission in submissions:
        check_and_update_tasks_by_account(submission.task_id, db)

    # 重新查询以获取最新状态，并添加任务和创建者信息
    if current_user.role == "admin":
        submissions = db.query(AccountSubmission).join(Task, AccountSubmission.task_id == Task.id).join(User, Task.creator_id == User.id).order_by(AccountSubmission.submitted_at.desc()).offset(skip).limit(limit).all()
    else:
        submissions = db.query(AccountSubmission).join(Task, AccountSubmission.task_id == Task.id).join(User, Task.creator_id == User.id).filter(Task.creator_id == current_user.id).order_by(AccountSubmission.submitted_at.desc()).offset(skip).limit(limit).all()

    # 为每个账号提交添加任务和创建者信息
    for submission in submissions:
        task = db.query(Task).filter(Task.id == submission.task_id).first()
        if task:
            submission.task_title = task.title
            submission.task_type = task.task_type
            creator = db.query(User).filter(User.id == task.creator_id).first()
            if creator:
                submission.task_creator_username = creator.username

    return {
        "items": submissions,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.get("/{account_id}", response_model=AccountSubmissionResponse)
def get_account_submission(
    account_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取账号提交详情"""
    # 获取当前用户的所有任务ID
    user_task_ids = db.query(Task.id).filter(Task.creator_id == current_user.id).subquery()
    
    submission = db.query(AccountSubmission).filter(
        AccountSubmission.id == account_id,
        AccountSubmission.task_id.in_(user_task_ids)
    ).first()
    
    if not submission:
        raise HTTPException(status_code=404, detail="Account submission not found")
    
    return submission


@router.put("/{account_id}", response_model=AccountSubmissionResponse)
def update_account_submission(
    account_id: int,
    account_update: AccountSubmissionCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新账号提交信息"""
    # 获取当前用户的所有任务ID
    user_task_ids = db.query(Task.id).filter(Task.creator_id == current_user.id).subquery()

    submission = db.query(AccountSubmission).filter(
        AccountSubmission.id == account_id,
        AccountSubmission.task_id.in_(user_task_ids)
    ).first()

    if not submission:
        raise HTTPException(status_code=404, detail="Account submission not found")

    # 更新字段
    submission.username = account_update.username
    submission.password = account_update.password
    submission.email = account_update.email
    submission.level_info = account_update.level_info
    submission.key_info = account_update.key_info
    submission.need_notification = account_update.need_notification

    # 同时更新对应任务的标题
    task = db.query(Task).filter(Task.id == submission.task_id).first()
    if task:
        new_title = f"{account_update.username}+{task.task_type}"
        task.title = new_title

    db.commit()
    db.refresh(submission)

    return submission


@router.put("/{account_id}/status")
def update_account_status(
    account_id: int,
    status: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新账号状态（管理员可更新所有账号状态，普通用户只能更新自己任务的账号状态）"""
    # 验证状态值 - 账号只有这三种状态
    valid_statuses = ["进行中", "已完成", "已取消"]
    if status not in valid_statuses:
        raise HTTPException(status_code=400, detail="Invalid status")

    # 构建查询条件
    if current_user.role == "admin":
        # 管理员可以更新所有账号状态
        submission = db.query(AccountSubmission).filter(
            AccountSubmission.id == account_id
        ).first()
    else:
        # 普通用户只能更新自己任务的账号状态
        user_task_ids = db.query(Task.id).filter(Task.creator_id == current_user.id).subquery()
        submission = db.query(AccountSubmission).filter(
            AccountSubmission.id == account_id,
            AccountSubmission.task_id.in_(user_task_ids)
        ).first()

    if not submission:
        raise HTTPException(status_code=404, detail="Account submission not found")
    
    old_status = submission.status
    submission.status = status

    # 同步更新对应任务状态
    if status != old_status:
        task = db.query(Task).filter(Task.id == submission.task_id).first()
        if task:
            # 账号状态变化时，任务状态也相应变化
            task.status = status

    db.commit()

    return {"message": "Account status updated successfully"}


@router.post("/admin/create", response_model=AccountSubmissionResponse)
def admin_create_account(
    account_data: AdminAccountCreate,
    current_user: User = Depends(check_admin_permission),
    db: Session = Depends(get_db)
):
    """管理员手动添加账号"""
    # 生成分享令牌和过期时间
    share_token = generate_share_token()
    expires_at = datetime.utcnow() + timedelta(days=account_data.duration_days + 7)

    # 创建任务标题
    title = f"{account_data.username}+{account_data.task_type}"

    # 创建任务
    task = Task(
        title=title,
        description=account_data.description,
        task_type=account_data.task_type,
        duration_days=account_data.duration_days,
        status="进行中",  # 直接设置为进行中状态
        share_token=share_token,
        creator_id=current_user.id,
        expires_at=expires_at
    )

    db.add(task)
    db.flush()  # 获取任务ID

    # 创建账号提交记录
    submission = AccountSubmission(
        task_id=task.id,
        username=account_data.username,
        password=account_data.password,
        email=account_data.email,
        level_info=account_data.level_info,
        key_info=account_data.key_info,
        need_notification=account_data.need_notification,
        status="进行中"  # 账号一旦提交就是进行中状态
    )

    db.add(submission)
    db.commit()
    db.refresh(submission)

    return submission
