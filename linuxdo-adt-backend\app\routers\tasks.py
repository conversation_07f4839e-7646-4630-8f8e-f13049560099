import secrets
from datetime import datetime, timedelta
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ..database import get_db
from ..models import User, Task
from ..schemas import TaskCreate, TaskUpdate, TaskResponse
from ..auth import get_current_active_user
from ..email_verification_required import require_email_verification
from ..task_scheduler import check_and_update_task_status, update_expired_tasks
from ..email_hooks import email_hooks

router = APIRouter(prefix="/api/tasks", tags=["任务管理"])


def generate_share_token() -> str:
    """生成分享令牌"""
    return secrets.token_urlsafe(32)


@router.get("/")
def get_tasks(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取当前用户的任务列表"""
    # 获取总数
    total = db.query(Task).filter(Task.creator_id == current_user.id).count()

    # 获取分页数据，按创建时间倒序排列（最新的在前面）
    tasks = db.query(Task).filter(Task.creator_id == current_user.id).order_by(Task.created_at.desc()).offset(skip).limit(limit).all()

    # 检查并更新过期任务状态
    for task in tasks:
        check_and_update_task_status(task.id, db)

    # 重新查询以获取最新状态
    tasks = db.query(Task).filter(Task.creator_id == current_user.id).order_by(Task.created_at.desc()).offset(skip).limit(limit).all()

    return {
        "items": tasks,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.post("/", response_model=TaskResponse)
def create_task(
    task: TaskCreate,
    current_user: User = Depends(require_email_verification),
    db: Session = Depends(get_db)
):
    """创建新任务"""
    share_token = generate_share_token()
    expires_at = datetime.utcnow() + timedelta(days=task.duration_days + 7)  # 任务期限 + 7天缓冲
    
    db_task = Task(
        title=task.title,
        description=task.description,
        task_type=task.task_type,
        duration_days=task.duration_days,
        share_token=share_token,
        creator_id=current_user.id,
        expires_at=expires_at
    )
    
    db.add(db_task)
    db.commit()
    db.refresh(db_task)
    
    return db_task


@router.get("/{task_id}", response_model=TaskResponse)
def get_task(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取任务详情"""
    task = db.query(Task).filter(
        Task.id == task_id,
        Task.creator_id == current_user.id
    ).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return task


@router.get("/{task_id}", response_model=TaskResponse)
def get_task(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取单个任务详情"""
    task = db.query(Task).filter(
        Task.id == task_id,
        Task.creator_id == current_user.id
    ).first()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 检查并更新任务状态
    check_and_update_task_status(task_id, db)

    # 重新查询以获取最新状态
    task = db.query(Task).filter(
        Task.id == task_id,
        Task.creator_id == current_user.id
    ).first()

    return task


@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: int,
    task_update: TaskUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新任务"""
    task = db.query(Task).filter(
        Task.id == task_id,
        Task.creator_id == current_user.id
    ).first()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 更新字段
    update_data = task_update.dict(exclude_unset=True)
    old_status = task.status

    for field, value in update_data.items():
        setattr(task, field, value)

    # 如果任务状态发生变化，同步更新对应账号的状态
    if 'status' in update_data and update_data['status'] != old_status:
        from ..models import AccountSubmission
        new_status = update_data['status']

        # 只有当任务状态不是"待提交"时，才同步账号状态
        # 因为"待提交"状态时还没有账号记录
        if new_status != "待提交":
            db.query(AccountSubmission).filter(
                AccountSubmission.task_id == task_id
            ).update({"status": new_status})

    db.commit()
    db.refresh(task)

    # 如果任务状态变为已完成或已取消，触发邮件通知
    if 'status' in update_data and update_data['status'] != old_status:
        try:
            if update_data['status'] == "已完成":
                await email_hooks.trigger("task_completed", db=db, task=task, user=task.creator)
            elif update_data['status'] == "已取消":
                await email_hooks.trigger("task_cancelled", db=db, task=task, user=task.creator)
        except Exception as e:
            # 邮件发送失败不影响任务更新
            print(f"发送任务状态变更邮件失败: {str(e)}")

    return task


@router.delete("/{task_id}")
def delete_task(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除任务"""
    task = db.query(Task).filter(
        Task.id == task_id,
        Task.creator_id == current_user.id
    ).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    db.delete(task)
    db.commit()
    
    return {"message": "Task deleted successfully"}


@router.post("/{task_id}/share")
def regenerate_share_token(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """重新生成分享链接"""
    task = db.query(Task).filter(
        Task.id == task_id,
        Task.creator_id == current_user.id
    ).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task.share_token = generate_share_token()
    db.commit()
    
    return {"share_token": task.share_token, "share_url": f"/share/{task.share_token}"}


@router.post("/admin/update-expired")
def admin_update_expired_tasks(
    current_user: User = Depends(get_current_active_user)
):
    """管理员手动更新过期任务状态"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )

    updated_count = update_expired_tasks()
    return {
        "message": f"成功更新了 {updated_count} 个过期任务的状态",
        "updated_count": updated_count
    }
