from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional
from ..database import get_db
from ..models import Hitoko<PERSON>, User
from ..schemas import HitokotoCreate, HitokotoResponse
from ..auth import get_current_user

router = APIRouter(prefix="/api/hitokoto", tags=["一言"])


def check_admin_permission(current_user: User = Depends(get_current_user)):
    """检查管理员权限"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


@router.post("/store", response_model=HitokotoResponse)
def store_hitokoto(
    hitokoto_data: HitokotoCreate,
    db: Session = Depends(get_db)
):
    """存储一言数据（公开接口）"""
    try:
        # 检查是否已存在相同uuid的一言
        existing = db.query(Hitokoto).filter(Hitokoto.uuid == hitokoto_data.uuid).first()
        if existing:
            return existing
        
        # 创建新的一言记录
        db_hitokoto = Hitokoto(
            hitokoto_id=hitokoto_data.hitokoto_id,
            uuid=hitokoto_data.uuid,
            hitokoto=hitokoto_data.hitokoto,
            type=hitokoto_data.type,
            from_text=hitokoto_data.from_text,
            from_who=hitokoto_data.from_who,
            creator=hitokoto_data.creator,
            creator_uid=hitokoto_data.creator_uid,
            reviewer=hitokoto_data.reviewer,
            commit_from=hitokoto_data.commit_from,
            hitokoto_created_at=hitokoto_data.hitokoto_created_at,
            length=hitokoto_data.length
        )
        
        db.add(db_hitokoto)
        db.commit()
        db.refresh(db_hitokoto)
        return db_hitokoto
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"存储一言数据失败: {str(e)}"
        )


@router.get("/list", response_model=List[HitokotoResponse])
def get_hitokoto_list(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取一言列表（管理员接口）"""
    hitokoto_list = db.query(Hitokoto).order_by(Hitokoto.created_at.desc()).offset(skip).limit(limit).all()
    return hitokoto_list


@router.get("/stats")
def get_hitokoto_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取一言统计信息（管理员接口）"""
    total_count = db.query(Hitokoto).count()
    
    # 按类型统计
    type_stats = db.query(Hitokoto.type, func.count(Hitokoto.id)).group_by(Hitokoto.type).all()
    type_distribution = {type_name or "未知": count for type_name, count in type_stats}
    
    return {
        "total_count": total_count,
        "type_distribution": type_distribution
    }


@router.delete("/{hitokoto_id}")
def delete_hitokoto(
    hitokoto_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """删除一言记录（管理员接口）"""
    hitokoto = db.query(Hitokoto).filter(Hitokoto.id == hitokoto_id).first()
    if not hitokoto:
        raise HTTPException(status_code=404, detail="一言记录不存在")
    
    db.delete(hitokoto)
    db.commit()
    return {"message": "一言记录已删除"}


@router.get("/random", response_model=HitokotoResponse)
def get_random_hitokoto(
    db: Session = Depends(get_db)
):
    """获取随机一言（公开接口）"""
    hitokoto = db.query(Hitokoto).order_by(func.random()).first()
    if not hitokoto:
        raise HTTPException(status_code=404, detail="暂无一言数据")
    return hitokoto
