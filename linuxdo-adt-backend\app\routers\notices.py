from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ..database import get_db
from ..models import User, SystemNotice
from ..schemas import SystemNoticeCreate, SystemNoticeResponse
from ..auth import get_current_active_user

router = APIRouter(prefix="/api/notices", tags=["系统提示"])


@router.get("/", response_model=List[SystemNoticeResponse])
def get_notices(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取系统提示信息列表"""
    notices = db.query(SystemNotice).order_by(SystemNotice.created_at.desc()).offset(skip).limit(limit).all()
    return notices


@router.post("/", response_model=SystemNoticeResponse)
def create_notice(
    notice: SystemNoticeCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建系统提示信息"""
    db_notice = SystemNotice(
        title=notice.title,
        content=notice.content,
        priority=notice.priority,
        is_active=notice.is_active
    )
    
    db.add(db_notice)
    db.commit()
    db.refresh(db_notice)
    
    return db_notice


@router.put("/{notice_id}", response_model=SystemNoticeResponse)
def update_notice(
    notice_id: int,
    notice: SystemNoticeCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新系统提示信息"""
    db_notice = db.query(SystemNotice).filter(SystemNotice.id == notice_id).first()
    
    if not db_notice:
        raise HTTPException(status_code=404, detail="Notice not found")
    
    db_notice.title = notice.title
    db_notice.content = notice.content
    db_notice.priority = notice.priority
    db_notice.is_active = notice.is_active
    
    db.commit()
    db.refresh(db_notice)
    
    return db_notice


@router.delete("/{notice_id}")
def delete_notice(
    notice_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除系统提示信息"""
    db_notice = db.query(SystemNotice).filter(SystemNotice.id == notice_id).first()
    
    if not db_notice:
        raise HTTPException(status_code=404, detail="Notice not found")
    
    db.delete(db_notice)
    db.commit()
    
    return {"message": "Notice deleted successfully"}
