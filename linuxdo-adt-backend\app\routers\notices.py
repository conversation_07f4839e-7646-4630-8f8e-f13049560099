from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ..database import get_db
from ..models import User, SystemNotice
from ..schemas import SystemNoticeCreate, SystemNoticeResponse
from ..auth import get_current_active_user

router = APIRouter(prefix="/api/notices", tags=["系统提示"])


@router.get("/", response_model=List[SystemNoticeResponse])
def get_notices(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取系统提示信息列表（所有用户都可以查看所有提示）"""
    # 使用关联查询获取创建者信息
    notices = db.query(SystemNotice).join(User, SystemNotice.creator_id == User.id).order_by(SystemNotice.created_at.desc()).offset(skip).limit(limit).all()

    # 为每个提示添加创建者用户名
    for notice in notices:
        creator = db.query(User).filter(User.id == notice.creator_id).first()
        if creator:
            notice.creator_username = creator.username

    return notices


@router.post("/", response_model=SystemNoticeResponse)
def create_notice(
    notice: SystemNoticeCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建系统提示信息"""
    db_notice = SystemNotice(
        title=notice.title,
        content=notice.content,
        priority=notice.priority,
        is_active=notice.is_active,
        creator_id=current_user.id
    )

    db.add(db_notice)
    db.commit()
    db.refresh(db_notice)

    # 添加创建者用户名
    db_notice.creator_username = current_user.username

    return db_notice


@router.put("/{notice_id}", response_model=SystemNoticeResponse)
def update_notice(
    notice_id: int,
    notice: SystemNoticeCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新系统提示信息（管理员可更新所有提示，普通用户只能更新自己创建的提示）"""
    db_notice = db.query(SystemNotice).filter(SystemNotice.id == notice_id).first()

    if not db_notice:
        raise HTTPException(status_code=404, detail="Notice not found")

    # 权限检查：普通用户只能更新自己创建的提示
    if current_user.role != "admin" and db_notice.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="您只能修改自己创建的系统提示"
        )

    db_notice.title = notice.title
    db_notice.content = notice.content
    db_notice.priority = notice.priority
    db_notice.is_active = notice.is_active

    db.commit()
    db.refresh(db_notice)

    # 添加创建者用户名
    creator = db.query(User).filter(User.id == db_notice.creator_id).first()
    if creator:
        db_notice.creator_username = creator.username

    return db_notice


@router.delete("/{notice_id}")
def delete_notice(
    notice_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除系统提示信息（管理员可删除所有提示，普通用户只能删除自己创建的提示）"""
    db_notice = db.query(SystemNotice).filter(SystemNotice.id == notice_id).first()

    if not db_notice:
        raise HTTPException(status_code=404, detail="Notice not found")

    # 权限检查：普通用户只能删除自己创建的提示
    if current_user.role != "admin" and db_notice.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="您只能删除自己创建的系统提示"
        )

    db.delete(db_notice)
    db.commit()

    return {"message": "Notice deleted successfully"}
