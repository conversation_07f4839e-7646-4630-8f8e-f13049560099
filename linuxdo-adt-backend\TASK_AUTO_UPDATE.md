# 任务自动状态更新功能

## 功能概述

系统现在支持任务到期后自动转换为"已完成"状态，包含以下两种更新机制：

### 1. 定时自动更新（服务端定时任务）

- **更新频率**: 每5分钟检查一次
- **更新条件**: 任务过期时间 <= 当前时间 且 状态为"进行中"
- **更新结果**: 自动将状态改为"已完成"
- **日志记录**: 所有更新操作都会记录到日志中

### 2. 接口调用时检查更新

在以下接口调用时会自动检查并更新相关任务状态：

#### 任务相关接口
- `GET /api/tasks/` - 获取任务列表
- `GET /api/tasks/{task_id}` - 获取单个任务详情

#### 账号相关接口  
- `GET /api/accounts/` - 获取账号列表

## 实现细节

### 核心文件

1. **`app/task_scheduler.py`** - 任务调度器核心逻辑
   - `update_expired_tasks()` - 批量更新过期任务
   - `check_and_update_task_status()` - 检查单个任务状态
   - `start_task_scheduler()` - 启动后台定时任务

2. **`app/main.py`** - 应用启动时初始化调度器
   - 在应用启动事件中调用 `init_task_scheduler()`

3. **`app/routers/tasks.py`** - 任务路由增强
   - 在获取任务时检查状态
   - 新增管理员手动更新接口

4. **`app/routers/accounts.py`** - 账号路由增强
   - 在获取账号时检查相关任务状态

### 状态更新逻辑

```python
# 更新条件
if task.expires_at <= current_time and task.status == "进行中":
    task.status = "已完成"
    task.updated_at = current_time
```

### 管理员接口

**手动触发更新**:
```
POST /api/tasks/admin/update-expired
```

需要管理员权限，返回更新的任务数量。

## 测试

运行测试脚本验证功能：

```bash
cd linuxdo-adt-backend
python test_task_expiry.py
```

测试内容：
- 创建过期的测试任务
- 验证批量更新功能
- 验证单个任务检查功能
- 自动清理测试数据

## 日志

系统会记录以下日志信息：
- 任务调度器启动
- 每次批量更新的结果
- 单个任务状态变更
- 错误信息

日志格式：
```
2025-07-27 10:30:00 - app.task_scheduler - INFO - 任务 123 (测试任务) 已自动更新为已完成状态
```

## 注意事项

1. **时区处理**: 使用 `china_now()` 确保时间一致性
2. **数据库事务**: 所有更新操作都在事务中进行
3. **错误处理**: 包含完整的异常处理和回滚机制
4. **性能考虑**: 定时任务频率设置为5分钟，避免过于频繁的数据库查询

## 部署说明

1. 确保所有新文件都已部署到服务器
2. 重启应用以启动任务调度器
3. 检查日志确认调度器正常运行
4. 可通过管理员接口手动触发更新进行测试
