<template>
  <div class="task-list">
    <div class="header-actions">
      <h3>任务管理</h3>
      <el-button type="primary" @click="openCreateDialog">
        <el-icon><Plus /></el-icon>
        创建任务
      </el-button>
    </div>

    <!-- 搜索和过滤区域 -->
    <div class="search-filters">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchText"
            placeholder="搜索任务标题或描述"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="filterTaskType"
            placeholder="任务类型"
            clearable
            @change="handleFilter"
          >
            <el-option label="升2级" value="升2级" />
            <el-option label="升3级" value="升3级" />
            <el-option label="保号" value="保号" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="filterStatus"
            placeholder="任务状态"
            clearable
            @change="handleFilter"
          >
            <el-option label="待提交" value="待提交" />
            <el-option label="进行中" value="进行中" />
            <el-option label="已完成" value="已完成" />
            <el-option label="已取消" value="已取消" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <div style="display: flex; gap: 5px;">
            <el-select
              v-model="durationOperator"
              placeholder="比较"
              clearable
              style="width: 70px"
              @change="handleFilter"
            >
              <el-option label=">" value=">" />
              <el-option label="=" value="=" />
              <el-option label="<" value="<" />
            </el-select>
            <el-select
              v-model="filterDuration"
              placeholder="天数"
              clearable
              style="flex: 1"
              @change="handleFilter"
            >
              <el-option label="7天" value="7" />
              <el-option label="15天" value="15" />
              <el-option label="30天" value="30" />
              <el-option label="50天" value="50" />
              <el-option label="60天" value="60" />
              <el-option label="90天" value="90" />
            </el-select>
          </div>
        </el-col>
        <el-col :span="2">
          <el-button @click="resetFilters">重置</el-button>
        </el-col>
      </el-row>
    </div>
    
    <el-table :data="tasks" v-loading="loading" style="width: 100%" :row-class-name="getRowClassName">
      <el-table-column label="序号" width="80" type="index" :index="(index: number) => (currentPage - 1) * pageSize + index + 1" />
      <el-table-column prop="title" label="任务标题" min-width="200" show-overflow-tooltip />
      <el-table-column prop="task_type" label="任务类型" width="120" />
      <el-table-column prop="duration_days" label="代挂天数" width="100" />
      <!-- 管理员可以看到创建者列 -->
      <el-table-column v-if="isAdmin" prop="creator_username" label="创建者" width="120" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="结束时间" width="180">
        <template #default="scope">
          {{ formatEndDate(scope.row.created_at, scope.row.duration_days) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="copyShareLink(scope.row)">
            <el-icon><Share /></el-icon>
            复制分享链接
          </el-button>
          <el-button size="small" type="warning" @click="regenerateToken(scope.row)">
            <el-icon><Refresh /></el-icon>
            重新生成
          </el-button>
          <el-button size="small" type="danger" @click="deleteTask(scope.row)">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建任务对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建任务" width="500px">
      <el-form :model="taskForm" :rules="taskRules" ref="taskFormRef" label-width="100px">
        <el-form-item label="任务标题" prop="title">
          <el-input v-model="taskForm.title" placeholder="系统自动生成" disabled />
        </el-form-item>
        <el-form-item label="任务描述" prop="description">
          <el-input v-model="taskForm.description" type="textarea" placeholder="请输入任务描述" />
        </el-form-item>
        <el-form-item label="任务类型" prop="task_type">
          <el-select v-model="taskForm.task_type" placeholder="请选择任务类型">
            <el-option label="升2级" value="升2级" />
            <el-option label="升3级" value="升3级" />
            <el-option label="保号" value="保号" />
          </el-select>
        </el-form-item>
        <el-form-item label="代挂天数" prop="duration_days">
          <el-input-number v-model="taskForm.duration_days" :min="1" :max="365" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreateTask" :loading="creating">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import { getTasks, createTask, deleteTask as deleteTaskApi, regenerateShareToken } from '../api/tasks'
import type { Task, TaskCreate } from '../api/tasks'
import api from '../api/index'

const tasks = ref<Task[]>([])
const allTasks = ref<Task[]>([]) // 存储所有任务数据
const loading = ref(false)
const showCreateDialog = ref(false)
const creating = ref(false)

// 当前用户信息
const currentUser = ref<any>(null)
const isAdmin = ref(false)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(50)
const total = ref(0)

// 搜索和过滤相关
const searchText = ref('')
const filterTaskType = ref('')
const filterStatus = ref('')
const filterDuration = ref('') // 默认为空
const durationOperator = ref('') // 默认为空

// 路由和高亮相关
const route = useRoute()
const highlightTaskId = ref<number | null>(null)

const taskForm = reactive<TaskCreate>({
  title: '',
  description: '',
  task_type: '',
  duration_days: 1
})

const taskRules = {
  title: [{ required: true, message: '请输入任务标题', trigger: 'blur' }],
  task_type: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
  duration_days: [{ required: true, message: '请输入代挂天数', trigger: 'blur' }]
}

const taskFormRef = ref()

// 生成UUID函数
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c == 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

// 打开创建对话框
const openCreateDialog = () => {
  // 生成UUID作为任务标题
  taskForm.title = generateUUID()
  showCreateDialog.value = true
}

const loadTasks = async () => {
  loading.value = true
  try {
    // 获取所有数据用于前端过滤
    const response = await getTasks(0, 10000) // 获取大量数据

    // 适应新的API响应格式
    if ((response.data as any).items) {
      allTasks.value = (response.data as any).items
    } else {
      // 兼容旧格式
      allTasks.value = response.data as any
    }

    // 应用过滤和搜索
    applyFilters()
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

// 应用过滤和搜索
const applyFilters = () => {
  let filteredTasks = [...allTasks.value]

  // 搜索过滤
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase()
    filteredTasks = filteredTasks.filter(task =>
      task.title.toLowerCase().includes(searchLower) ||
      (task.description && task.description.toLowerCase().includes(searchLower))
    )
  }

  // 任务类型过滤
  if (filterTaskType.value) {
    filteredTasks = filteredTasks.filter(task => task.task_type === filterTaskType.value)
  }

  // 状态过滤
  if (filterStatus.value) {
    filteredTasks = filteredTasks.filter(task => task.status === filterStatus.value)
  }

  // 代挂天数过滤 - 只有在操作符和天数都选择时才过滤
  if (filterDuration.value && durationOperator.value) {
    const targetDays = parseInt(filterDuration.value)
    filteredTasks = filteredTasks.filter(task => {
      const taskDays = task.duration_days
      switch (durationOperator.value) {
        case '>':
          return taskDays > targetDays
        case '=':
          return taskDays === targetDays
        case '<':
          return taskDays < targetDays
        default:
          return true
      }
    })
  }

  // 更新总数和分页
  total.value = filteredTasks.length

  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  tasks.value = filteredTasks.slice(start, end)
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1 // 重置到第一页
  applyFilters()
}

// 过滤处理
const handleFilter = () => {
  currentPage.value = 1 // 重置到第一页
  applyFilters()
}

// 重置过滤器
const resetFilters = () => {
  searchText.value = ''
  filterTaskType.value = ''
  filterStatus.value = ''
  filterDuration.value = '' // 重置为空
  durationOperator.value = '' // 重置为空
  currentPage.value = 1
  applyFilters()
}

// 分页事件处理
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1
  applyFilters()
}

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  applyFilters()
}

const handleCreateTask = async () => {
  if (!taskFormRef.value) return
  
  await taskFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      creating.value = true
      try {
        await createTask(taskForm)
        ElMessage.success('任务创建成功')
        showCreateDialog.value = false
        Object.assign(taskForm, { title: '', description: '', task_type: '', duration_days: 1 })
        loadTasks()
      } catch (error: any) {
        ElMessage.error(error.response?.data?.detail || '创建任务失败')
      } finally {
        creating.value = false
      }
    }
  })
}

const copyShareLink = (task: Task) => {
  const shareUrl = `${window.location.origin}/share/${task.share_token}`
  navigator.clipboard.writeText(shareUrl).then(() => {
    ElMessage.success('分享链接已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制')
  })
}

const regenerateToken = async (task: Task) => {
  try {
    await regenerateShareToken(task.id)
    ElMessage.success('分享链接已重新生成')
    loadTasks()
  } catch (error) {
    ElMessage.error('重新生成失败')
  }
}

const deleteTask = async (task: Task) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteTaskApi(task.id)
    ElMessage.success('任务删除成功')
    loadTasks()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
    }
  }
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待提交': 'info',
    '进行中': 'warning',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return statusMap[status] || 'info'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatEndDate = (createdAt: string, durationDays: number) => {
  const startDate = new Date(createdAt)
  const endDate = new Date(startDate.getTime() + durationDays * 24 * 60 * 60 * 1000)
  return endDate.toLocaleString('zh-CN')
}

// 获取行类名（用于高亮）
const getRowClassName = ({ row }: { row: Task }) => {
  return highlightTaskId.value === row.id ? 'highlight-row' : ''
}

// 处理高亮逻辑
const handleHighlight = () => {
  const highlightId = route.query.highlight
  if (highlightId) {
    highlightTaskId.value = parseInt(highlightId as string)

    // 3秒后取消高亮
    setTimeout(() => {
      highlightTaskId.value = null
    }, 3000)
  }
}

// 获取当前用户信息
const getCurrentUser = async () => {
  try {
    const response = await api.get('/api/auth/me')
    currentUser.value = response.data
    isAdmin.value = response.data.role === 'admin'
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

onMounted(async () => {
  await getCurrentUser()
  await loadTasks()

  // 处理高亮
  nextTick(() => {
    handleHighlight()
  })
})
</script>

<style scoped>
.task-list {
  background: white;
  padding: 20px;
  border-radius: 8px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions h3 {
  margin: 0;
}

/* 搜索过滤区域样式 */
.search-filters {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 高亮行样式 */
:deep(.highlight-row) {
  background-color: #fff7e6 !important;
  animation: highlight-fade 3s ease-out;
}

@keyframes highlight-fade {
  0% {
    background-color: #ffd666 !important;
  }
  100% {
    background-color: #fff7e6 !important;
  }
}
</style>
