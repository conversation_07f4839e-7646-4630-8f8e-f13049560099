# LinuxDo ADT 自动挂机系统

## 项目简介

LinuxDo ADT 是一个自动挂机系统的管理网站，用于收集和管理用户的账号信息，配合桌面端挂机软件使用。

## 功能特性

### 管理后台功能
- 用户认证系统（注册/登录）
- 任务管理（创建、查看、编辑、删除任务）
- 分享链接生成和管理
- 账号信息收集和状态管理
- 系统提示信息管理

### 分享页面功能
- 任务信息展示
- 系统提示信息显示
- 账号信息提交表单
- 提交状态反馈

## 技术栈

### 后端
- **框架**: FastAPI
- **数据库**: SQLite3
- **认证**: JWT
- **ORM**: SQLAlchemy
- **API文档**: 自动生成的Swagger文档

### 前端
- **框架**: Vue 3 + TypeScript
- **UI库**: Element Plus
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 项目结构

```
linuxdo_adt/
├── linuxdo-adt-backend/          # 后端代码
│   ├── app/
│   │   ├── routers/              # API路由
│   │   ├── models.py             # 数据库模型
│   │   ├── schemas.py            # Pydantic模型
│   │   ├── auth.py               # 认证相关
│   │   ├── database.py           # 数据库配置
│   │   ├── config.py             # 配置文件
│   │   └── main.py               # 主应用
│   ├── requirements.txt          # Python依赖
│   ├── .env                      # 环境变量
│   └── run.py                    # 启动脚本
├── linuxdo-adt-frontend/         # 前端代码
│   ├── src/
│   │   ├── api/                  # API接口
│   │   ├── views/                # 页面组件
│   │   ├── router/               # 路由配置
│   │   └── main.ts               # 主入口
│   └── package.json              # Node.js依赖
├── start_backend.bat             # 后端启动脚本
├── start_frontend.bat            # 前端启动脚本
└── README.md                     # 项目说明
```

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- npm 或 yarn

### 启动步骤

1. **启动后端服务**
   ```bash
   # 方式1：使用批处理文件（Windows）
   start_backend.bat
   
   # 方式2：手动启动
   cd linuxdo-adt-backend
   pip install -r requirements.txt
   python run.py
   ```

2. **启动前端服务**
   ```bash
   # 方式1：使用批处理文件（Windows）
   start_frontend.bat
   
   # 方式2：手动启动
   cd linuxdo-adt-frontend
   npm install
   npm run dev
   ```

3. **访问应用**
   - 管理后台: http://localhost:5173
   - API文档: http://localhost:8000/docs
   - 分享页面: http://localhost:5173/share/{share_token}

## 使用流程

### 管理员操作流程
1. 注册/登录管理后台
2. 创建新任务（填写任务类型、代挂天数等信息）
3. 复制分享链接发送给用户
4. 查看和管理提交的账号信息
5. 更新任务和账号状态

### 用户操作流程
1. 点击管理员提供的分享链接
2. 查看任务详情和系统提示
3. 填写LinuxDo账号信息（用户名、密码、邮箱）
4. 提交信息并等待处理

## API接口

### 认证相关
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录

### 任务管理
- `GET /api/tasks` - 获取任务列表
- `POST /api/tasks` - 创建任务
- `PUT /api/tasks/{task_id}` - 更新任务
- `DELETE /api/tasks/{task_id}` - 删除任务
- `POST /api/tasks/{task_id}/share` - 重新生成分享链接

### 分享功能
- `GET /api/share/{share_token}` - 获取分享任务信息
- `POST /api/share/{share_token}/submit` - 提交账号信息
- `GET /api/share/{share_token}/notices` - 获取系统提示

### 账号管理
- `GET /api/accounts` - 获取账号提交列表
- `PUT /api/accounts/{account_id}/status` - 更新账号状态

### 系统管理
- `GET /api/notices` - 获取系统提示列表
- `POST /api/notices` - 创建系统提示
- `PUT /api/notices/{notice_id}` - 更新系统提示
- `DELETE /api/notices/{notice_id}` - 删除系统提示

## 数据库设计

### 主要表结构
- **users**: 用户表
- **tasks**: 任务表
- **account_submissions**: 账号提交表
- **system_notices**: 系统提示表

## 配置说明

### 后端配置 (.env)
```
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
DATABASE_URL=sqlite:///./linuxdo_adt.db
```

### 前端配置
- API基础URL: `http://localhost:8000`
- 开发服务器端口: 5173

## 注意事项

1. **安全性**
   - 生产环境请修改SECRET_KEY
   - 建议使用HTTPS
   - 定期备份数据库

2. **部署**
   - 后端可使用uvicorn部署
   - 前端需要构建后部署到静态服务器
   - 注意CORS配置

3. **扩展性**
   - 支持添加更多任务类型
   - 可扩展账号状态管理
   - 支持自定义系统提示

## 开发说明

- 后端使用FastAPI自动生成API文档
- 前端使用TypeScript提供类型安全
- 使用Element Plus提供现代化UI
- 支持响应式设计

## 许可证

本项目仅供学习和研究使用。
