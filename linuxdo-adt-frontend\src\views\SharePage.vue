<template>
  <div class="share-page">
    <div class="container">
      <el-card class="task-card" v-loading="loading">
        <template #header>
          <h2>LinuxDo 代挂任务</h2>
        </template>
        
        <div v-if="taskInfo" class="task-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="任务标题">{{ taskInfo.title }}</el-descriptions-item>
            <el-descriptions-item label="任务类型">{{ taskInfo.task_type }}</el-descriptions-item>
            <el-descriptions-item label="代挂天数">{{ taskInfo.duration_days }} 天</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDate(taskInfo.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="结束时间">{{ formatEndDate(taskInfo.created_at, taskInfo.duration_days) }}</el-descriptions-item>
            <el-descriptions-item label="任务描述" :span="2">
              {{ taskInfo.description || '暂无描述' }}
            </el-descriptions-item>
          </el-descriptions>
          
          <!-- 系统提示信息 -->
          <div v-if="notices.length > 0" class="notices-section">
            <h3>重要提示</h3>
            <el-alert
              v-for="notice in notices"
              :key="notice.id"
              :title="notice.title"
              :description="notice.content"
              :type="getNoticeType(notice.priority)"
              show-icon
              :closable="false"
              style="margin-bottom: 10px"
            />
          </div>
          
          <!-- 账号信息提交表单 -->
          <div v-if="!submitted && !taskInfo?.is_submitted" class="submit-section">
            <h3>请填写您的LinuxDo账号信息</h3>
            <el-form :model="accountForm" :rules="accountRules" ref="accountFormRef" label-width="100px">
              <el-form-item label="用户名" prop="username">
                <el-input v-model="accountForm.username" placeholder="请输入LinuxDo用户名" />
              </el-form-item>
              <el-form-item label="密码" prop="password">
                <el-input
                  v-model="accountForm.password"
                  type="password"
                  placeholder="请输入LinuxDo密码"
                  show-password
                  @paste.prevent
                />
              </el-form-item>
              <el-form-item label="确认密码" prop="confirmPassword">
                <el-input
                  v-model="accountForm.confirmPassword"
                  type="password"
                  placeholder="请再次输入LinuxDo密码"
                  show-password
                  @paste.prevent
                />
              </el-form-item>
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="accountForm.email" placeholder="请输入LinuxDo邮箱" />
                <div class="form-tip">邮箱是用来每天发送代挂通知的，请尽量准确填写</div>
              </el-form-item>
              <el-form-item label="通知设置">
                <el-switch
                  v-model="accountForm.need_notification"
                  active-text="接收通知"
                  inactive-text="不接收通知"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSubmit" :loading="submitting" style="width: 100%">
                  确认提交
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 链接已填写完成提示 -->
          <div v-else-if="taskInfo?.is_submitted && !submitted" class="completed-section">
            <el-result icon="info" title="链接填写完成" sub-title="该任务已经有人提交了账号信息，无法重复提交。">
              <template #extra>
                <el-button type="primary" @click="$router.push('/')">返回首页</el-button>
              </template>
            </el-result>
          </div>

          <!-- 提交成功提示 -->
          <div v-else class="success-section">
            <el-result icon="success" title="提交成功" sub-title="您的账号信息已成功提交，我们将尽快开始代挂服务。">
              <template #extra>
                <el-button type="primary" @click="$router.push('/')">返回首页</el-button>
              </template>
            </el-result>
          </div>
        </div>
        
        <div v-else-if="!loading" class="error-section">
          <el-result icon="error" title="任务不存在" sub-title="该分享链接无效或任务已过期">
            <template #extra>
              <el-button type="primary" @click="$router.push('/')">返回首页</el-button>
            </template>
          </el-result>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getSharedTask, submitAccountInfo, getNoticesForShare } from '../api/share'
import type { SharedTaskInfo, AccountSubmission, SystemNotice } from '../api/share'

const route = useRoute()
const shareToken = route.params.shareToken as string

const loading = ref(false)
const submitting = ref(false)
const submitted = ref(false)
const taskInfo = ref<SharedTaskInfo | null>(null)
const notices = ref<SystemNotice[]>([])

const accountForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  email: '',
  level_info: '',
  key_info: '',
  need_notification: true
})

const accountRules = {
  username: [{ required: true, message: '请输入LinuxDo用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入LinuxDo密码', trigger: 'blur' }],
  confirmPassword: [
    { required: true, message: '请再次输入LinuxDo密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== accountForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  email: [
    { required: true, message: '请输入LinuxDo邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

const accountFormRef = ref()

const loadTaskInfo = async () => {
  loading.value = true
  try {
    const [taskResponse, noticesResponse] = await Promise.all([
      getSharedTask(shareToken),
      getNoticesForShare(shareToken)
    ])
    
    taskInfo.value = taskResponse.data
    notices.value = noticesResponse.data
  } catch (error: any) {
    if (error.response?.status === 404) {
      ElMessage.error('任务不存在或已过期')
    } else {
      ElMessage.error('加载任务信息失败')
    }
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  if (!accountFormRef.value) return

  await accountFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitting.value = true
      try {
        // 只提交需要的字段，不包含confirmPassword
        const submitData: AccountSubmission = {
          username: accountForm.username,
          password: accountForm.password,
          email: accountForm.email,
          level_info: accountForm.level_info,
          key_info: accountForm.key_info,
          need_notification: accountForm.need_notification
        }
        await submitAccountInfo(shareToken, submitData)
        ElMessage.success('账号信息提交成功')
        submitted.value = true
      } catch (error: any) {
        ElMessage.error(error.response?.data?.detail || '提交失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatEndDate = (createdAt: string, durationDays: number) => {
  const startDate = new Date(createdAt)
  const endDate = new Date(startDate.getTime() + durationDays * 24 * 60 * 60 * 1000)
  return endDate.toLocaleString('zh-CN')
}

const getNoticeType = (priority: string) => {
  const typeMap: Record<string, string> = {
    'info': 'info',
    'warning': 'warning',
    'error': 'error',
    'success': 'success'
  }
  return typeMap[priority] || 'info'
}

onMounted(() => {
  loadTaskInfo()
})
</script>

<style scoped>
.share-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
}

.task-card {
  margin-top: 50px;
}

.task-info {
  margin-top: 20px;
}

.notices-section {
  margin: 30px 0;
}

.notices-section h3 {
  margin-bottom: 15px;
  color: #409eff;
}

.submit-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.submit-section h3 {
  margin-bottom: 20px;
  color: #333;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.completed-section {
  margin-top: 30px;
}

.success-section {
  margin-top: 30px;
}

.error-section {
  margin-top: 30px;
}
</style>
