import aiosmtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from jinja2 import Template
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from .models import EmailLog, EmailTemplate, SystemSettings
from .database import get_db
from datetime import datetime, timezone, timedelta

# 定义东八区时区
CHINA_TZ = timezone(timedelta(hours=8))

logger = logging.getLogger(__name__)


class EmailService:
    def __init__(self):
        self.smtp_server = None
        self.smtp_port = None
        self.smtp_username = None
        self.smtp_password = None
        self.smtp_use_tls = True
        self.from_email = None
        self.from_name = None

    async def load_settings(self, db: Session):
        """从数据库加载邮件配置"""
        settings_map = {}
        settings = db.query(SystemSettings).filter(
            SystemSettings.key.in_([
                'smtp_server', 'smtp_port', 'smtp_username', 'smtp_password',
                'smtp_use_tls', 'from_email', 'from_name'
            ])
        ).all()
        
        for setting in settings:
            settings_map[setting.key] = setting.value
        
        self.smtp_server = settings_map.get('smtp_server')
        self.smtp_port = int(settings_map.get('smtp_port', 587))
        self.smtp_username = settings_map.get('smtp_username')
        self.smtp_password = settings_map.get('smtp_password')
        self.smtp_use_tls = settings_map.get('smtp_use_tls', 'true').lower() == 'true'
        self.from_email = settings_map.get('from_email')
        self.from_name = settings_map.get('from_name', 'LinuxDo ADT')

    def render_template(self, template_content: str, variables: Dict[str, Any]) -> str:
        """渲染邮件模板"""
        template = Template(template_content)
        return template.render(**variables)

    async def send_email(
        self,
        to_email: str,
        subject: str,
        content: str,
        template_name: Optional[str] = None,
        template_variables: Optional[Dict[str, Any]] = None,
        db: Optional[Session] = None
    ) -> bool:
        """发送邮件"""
        if not db:
            return False
            
        # 加载邮件配置
        await self.load_settings(db)
        
        # 检查必要配置
        if not all([self.smtp_server, self.smtp_username, self.smtp_password, self.from_email]):
            logger.error("邮件配置不完整")
            return False

        # 创建邮件日志记录
        email_log = EmailLog(
            to_email=to_email,
            subject=subject,
            content=content,
            template_name=template_name,
            status="pending"
        )
        db.add(email_log)
        db.commit()
        db.refresh(email_log)

        try:
            # 如果使用模板，渲染内容
            if template_name and template_variables:
                template = db.query(EmailTemplate).filter(
                    EmailTemplate.name == template_name,
                    EmailTemplate.is_active == True
                ).first()
                
                if template:
                    subject = self.render_template(template.subject, template_variables)
                    content = self.render_template(template.content, template_variables)

            # 创建邮件
            message = MIMEMultipart()
            message["From"] = f"{self.from_name} <{self.from_email}>"
            message["To"] = to_email
            message["Subject"] = subject

            # 添加邮件内容
            message.attach(MIMEText(content, "html", "utf-8"))

            # 发送邮件
            await aiosmtplib.send(
                message,
                hostname=self.smtp_server,
                port=self.smtp_port,
                start_tls=self.smtp_use_tls,
                username=self.smtp_username,
                password=self.smtp_password,
            )

            # 更新日志状态
            email_log.status = "sent"
            email_log.sent_at = datetime.now(CHINA_TZ)
            db.commit()
            
            logger.info(f"邮件发送成功: {to_email}")
            return True

        except Exception as e:
            # 更新日志状态
            email_log.status = "failed"
            email_log.error_message = str(e)
            db.commit()
            
            logger.error(f"邮件发送失败: {to_email}, 错误: {str(e)}")
            return False

    async def send_registration_notification(self, user_email: str, username: str, db: Session) -> bool:
        """发送注册通知邮件"""
        return await self.send_email(
            to_email=user_email,
            subject="欢迎注册 LinuxDo ADT",
            content="",
            template_name="registration_welcome",
            template_variables={
                "username": username,
                "site_name": "LinuxDo ADT",
                "login_url": "http://localhost:5173/login"
            },
            db=db
        )

    async def send_task_notification(self, user_email: str, task_title: str, task_type: str, db: Session) -> bool:
        """发送任务通知邮件"""
        return await self.send_email(
            to_email=user_email,
            subject=f"任务通知: {task_title}",
            content="",
            template_name="task_notification",
            template_variables={
                "task_title": task_title,
                "task_type": task_type,
                "site_name": "LinuxDo ADT"
            },
            db=db
        )


# 全局邮件服务实例
email_service = EmailService()
