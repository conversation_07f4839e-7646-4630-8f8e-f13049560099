"""
邮件钩子系统 - 自动化邮件发送
通过事件触发，无需修改业务代码即可发送邮件
"""

import logging
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from .models import User, Task, AccountSubmission, EmailTemplate
from .email_service import email_service

logger = logging.getLogger(__name__)


class EmailHooks:
    """邮件钩子系统"""
    
    def __init__(self):
        # 事件到模板的映射配置
        self.event_template_mapping = {
            "user_registered": "registration_welcome",
            "task_created": "task_created",
            "task_completed": "task_completed",
            "task_cancelled": "task_cancelled",
            "account_submitted": "account_submitted",
            "email_verification": "email_verification",
            "password_reset_requested": "password_reset",
        }
    
    async def trigger(self, event_type: str, db: Session, **context) -> bool:
        """
        触发邮件事件
        
        Args:
            event_type: 事件类型
            db: 数据库会话
            **context: 上下文数据，如 user=user_obj, task=task_obj 等
        
        Returns:
            bool: 是否发送成功
        """
        try:
            # 检查是否有对应的模板配置
            template_name = self.event_template_mapping.get(event_type)
            if not template_name:
                logger.warning(f"未找到事件 {event_type} 对应的邮件模板")
                return False
            
            # 检查模板是否存在且启用
            template = db.query(EmailTemplate).filter(
                EmailTemplate.name == template_name,
                EmailTemplate.is_active == True
            ).first()
            
            if not template:
                logger.warning(f"邮件模板 {template_name} 不存在或未启用")
                return False
            
            # 根据事件类型构建邮件数据
            email_data = self._build_email_data(event_type, context)
            if not email_data:
                logger.error(f"无法为事件 {event_type} 构建邮件数据")
                return False
            
            # 发送邮件
            success = await email_service.send_email(
                to_email=email_data["to_email"],
                subject=email_data.get("subject", ""),
                content=email_data.get("content", ""),
                template_name=template_name,
                template_variables=email_data["template_variables"],
                db=db
            )
            
            if success:
                logger.info(f"邮件事件 {event_type} 触发成功，发送至 {email_data['to_email']}")
            else:
                logger.error(f"邮件事件 {event_type} 发送失败")
            
            return success
            
        except Exception as e:
            logger.error(f"触发邮件事件 {event_type} 时发生错误: {str(e)}")
            return False
    
    def _build_email_data(self, event_type: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """根据事件类型构建邮件数据"""
        
        if event_type == "user_registered":
            return self._build_user_registered_data(context)
        elif event_type == "task_created":
            return self._build_task_created_data(context)
        elif event_type == "task_completed":
            return self._build_task_completed_data(context)
        elif event_type == "task_cancelled":
            return self._build_task_cancelled_data(context)
        elif event_type == "account_submitted":
            return self._build_account_submitted_data(context)
        elif event_type == "email_verification":
            return self._build_email_verification_data(context)
        elif event_type == "password_reset_requested":
            return self._build_password_reset_data(context)
        else:
            logger.warning(f"未知的邮件事件类型: {event_type}")
            return None
    
    def _build_user_registered_data(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """构建用户注册邮件数据"""
        user = context.get("user")
        if not isinstance(user, User):
            return None
        
        return {
            "to_email": user.email,
            "template_variables": {
                "username": user.username,
                "user_email": user.email,
                "site_name": "LinuxDo ADT",
                "login_url": "http://localhost:5173/login"
            }
        }
    
    def _build_task_created_data(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """构建任务创建邮件数据"""
        task = context.get("task")
        user = context.get("user")
        
        if not isinstance(task, Task) or not isinstance(user, User):
            return None
        
        return {
            "to_email": user.email,
            "template_variables": {
                "username": user.username,
                "task_title": task.title,
                "task_type": task.task_type,
                "duration_days": task.duration_days,
                "task_status": task.status,
                "site_name": "LinuxDo ADT"
            }
        }
    
    def _build_task_completed_data(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """构建任务完成邮件数据"""
        task = context.get("task")
        user = context.get("user")
        account = context.get("account")  # 可选的账号信息
        
        if not isinstance(task, Task) or not isinstance(user, User):
            return None
        
        template_variables = {
            "username": user.username,
            "task_title": task.title,
            "task_type": task.task_type,
            "duration_days": task.duration_days,
            "task_status": task.status,
            "site_name": "LinuxDo ADT"
        }
        
        # 如果有账号信息，添加账号相关变量
        if isinstance(account, AccountSubmission):
            template_variables.update({
                "account_username": account.username,
                "account_email": account.email,
                "level_info": account.level_info
            })
        
        return {
            "to_email": user.email,
            "template_variables": template_variables
        }
    
    def _build_task_cancelled_data(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """构建任务取消邮件数据"""
        return self._build_task_completed_data(context)  # 复用相同的数据结构
    
    def _build_account_submitted_data(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """构建账号提交邮件数据"""
        account = context.get("account")
        task = context.get("task")
        user = context.get("user")
        
        if not isinstance(account, AccountSubmission) or not isinstance(task, Task):
            return None
        
        # 如果需要通知，发送给账号提供者
        if account.need_notification:
            to_email = account.email
        elif isinstance(user, User):
            to_email = user.email
        else:
            return None
        
        return {
            "to_email": to_email,
            "template_variables": {
                "account_username": account.username,
                "account_email": account.email,
                "level_info": account.level_info,
                "task_title": task.title,
                "task_type": task.task_type,
                "site_name": "LinuxDo ADT"
            }
        }
    
    def _build_email_verification_data(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """构建邮箱确认邮件数据"""
        user = context.get("user")
        verification_token = context.get("verification_token")
        verification_url = context.get("verification_url")
        email_to_verify = context.get("email_to_verify")  # 要验证的邮箱

        if not user or not verification_token:
            return None

        # 如果指定了要验证的邮箱，使用指定的邮箱，否则使用用户邮箱
        target_email = email_to_verify or user.email

        return {
            "to_email": target_email,
            "template_variables": {
                "username": user.username,
                "user_email": user.email,
                "email_to_verify": target_email,
                "verification_token": verification_token,
                "verification_url": verification_url or f"http://localhost:5173/verify-email?token={verification_token}",
                "site_name": "LinuxDo ADT"
            }
        }
    
    def _build_password_reset_data(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """构建密码重置邮件数据"""
        user = context.get("user")
        reset_token = context.get("reset_token")
        reset_url = context.get("reset_url")
        
        if not isinstance(user, User) or not reset_token:
            return None
        
        return {
            "to_email": user.email,
            "template_variables": {
                "username": user.username,
                "reset_token": reset_token,
                "reset_url": reset_url or f"http://localhost:5173/reset-password?token={reset_token}",
                "site_name": "LinuxDo ADT"
            }
        }
    
    def add_event_template_mapping(self, event_type: str, template_name: str):
        """动态添加事件到模板的映射"""
        self.event_template_mapping[event_type] = template_name
        logger.info(f"添加邮件事件映射: {event_type} -> {template_name}")
    
    def remove_event_template_mapping(self, event_type: str):
        """移除事件到模板的映射"""
        if event_type in self.event_template_mapping:
            del self.event_template_mapping[event_type]
            logger.info(f"移除邮件事件映射: {event_type}")


# 全局邮件钩子实例
email_hooks = EmailHooks()
