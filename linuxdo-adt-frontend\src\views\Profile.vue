<template>
  <div class="profile-container">
    <div class="profile-header">
      <h2>个人中心</h2>
      <p class="profile-subtitle">管理您的账户信息和安全设置</p>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：个人信息 -->
      <el-col :span="12">
        <el-card class="profile-card">
          <template #header>
            <div class="card-header">
              <span>个人信息</span>
            </div>
          </template>
          
          <el-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileRules"
            label-width="100px"
          >
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="profileForm.username"
                placeholder="请输入用户名"
                :disabled="!editingProfile"
              />
            </el-form-item>
            
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="profileForm.email"
                placeholder="请输入邮箱"
                :disabled="!editingProfile"
              />
            </el-form-item>
            
            <el-form-item label="角色">
              <el-tag :type="userInfo.role === 'admin' ? 'danger' : 'primary'">
                {{ userInfo.role === 'admin' ? '管理员' : '普通用户' }}
              </el-tag>
            </el-form-item>
            
            <el-form-item label="邮箱验证">
              <div class="email-verification">
                <el-tag :type="userInfo.email_verified ? 'success' : 'warning'">
                  {{ userInfo.email_verified ? '已验证' : '未验证' }}
                </el-tag>
                <el-button
                  v-if="!userInfo.email_verified"
                  type="primary"
                  size="small"
                  @click="sendVerificationEmail"
                  :loading="sendingVerification"
                  style="margin-left: 10px"
                >
                  发送验证邮件
                </el-button>
              </div>
            </el-form-item>
            
            <el-form-item label="注册时间">
              <span class="info-text">{{ formatDate(userInfo.created_at) }}</span>
            </el-form-item>
            
            <el-form-item>
              <el-button
                v-if="!editingProfile"
                type="primary"
                @click="startEditProfile"
              >
                编辑信息
              </el-button>
              <template v-else>
                <el-button
                  type="primary"
                  @click="saveProfile"
                  :loading="savingProfile"
                >
                  保存
                </el-button>
                <el-button @click="cancelEditProfile">
                  取消
                </el-button>
              </template>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 右侧：安全设置 -->
      <el-col :span="12">
        <el-card class="profile-card">
          <template #header>
            <div class="card-header">
              <span>安全设置</span>
            </div>
          </template>
          
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="100px"
          >
            <el-form-item label="当前密码" prop="currentPassword">
              <el-input
                v-model="passwordForm.currentPassword"
                type="password"
                placeholder="请输入当前密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                @click="changePassword"
                :loading="changingPassword"
              >
                修改密码
              </el-button>
              <el-button @click="resetPasswordForm">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 账户状态卡片 -->
        <el-card class="profile-card" style="margin-top: 20px">
          <template #header>
            <div class="card-header">
              <span>账户状态</span>
            </div>
          </template>
          
          <div class="status-item">
            <span class="status-label">账户状态：</span>
            <el-tag :type="userInfo.is_active ? 'success' : 'danger'">
              {{ userInfo.is_active ? '正常' : '已禁用' }}
            </el-tag>
          </div>
          
          <div class="status-item" v-if="userInfo.email_verified_at">
            <span class="status-label">邮箱验证时间：</span>
            <span class="info-text">{{ formatDate(userInfo.email_verified_at) }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import api from '../api/index'

// 响应式数据
const userInfo = ref({})
const editingProfile = ref(false)
const savingProfile = ref(false)
const changingPassword = ref(false)
const sendingVerification = ref(false)

// 表单引用
const profileFormRef = ref()
const passwordFormRef = ref()

// 个人信息表单
const profileForm = reactive({
  username: '',
  email: ''
})

// 原始个人信息备份
const originalProfileForm = reactive({
  username: '',
  email: ''
})

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const profileRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少 6 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const response = await api.get('/api/profile/')
    userInfo.value = response.data
    
    // 更新表单数据
    profileForm.username = userInfo.value.username
    profileForm.email = userInfo.value.email
    
    // 备份原始数据
    originalProfileForm.username = userInfo.value.username
    originalProfileForm.email = userInfo.value.email
  } catch (error) {
    ElMessage.error('加载用户信息失败')
  }
}

// 开始编辑个人信息
const startEditProfile = () => {
  editingProfile.value = true
}

// 取消编辑个人信息
const cancelEditProfile = () => {
  editingProfile.value = false
  // 恢复原始数据
  profileForm.username = originalProfileForm.username
  profileForm.email = originalProfileForm.email
  profileFormRef.value?.resetFields()
}

// 保存个人信息
const saveProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    savingProfile.value = true
    
    const updateData = {}
    if (profileForm.username !== originalProfileForm.username) {
      updateData.username = profileForm.username
    }
    if (profileForm.email !== originalProfileForm.email) {
      updateData.email = profileForm.email
    }
    
    if (Object.keys(updateData).length === 0) {
      ElMessage.info('没有修改任何信息')
      editingProfile.value = false
      return
    }
    
    await api.put('/api/profile/', updateData)
    ElMessage.success('个人信息更新成功')
    
    // 如果邮箱发生变化，提示用户验证邮箱
    if (updateData.email) {
      ElMessage.info('邮箱已更新，请重新验证邮箱')
    }
    
    editingProfile.value = false
    loadUserInfo() // 重新加载用户信息
  } catch (error) {
    if (error.response?.data?.detail) {
      ElMessage.error(error.response.data.detail)
    } else {
      ElMessage.error('更新个人信息失败')
    }
  } finally {
    savingProfile.value = false
  }
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    changingPassword.value = true
    
    await api.post('/api/profile/change-password', {
      current_password: passwordForm.currentPassword,
      new_password: passwordForm.newPassword
    })
    
    ElMessage.success('密码修改成功')
    resetPasswordForm()
  } catch (error) {
    if (error.response?.data?.detail) {
      ElMessage.error(error.response.data.detail)
    } else {
      ElMessage.error('修改密码失败')
    }
  } finally {
    changingPassword.value = false
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  passwordFormRef.value?.resetFields()
}

// 发送邮箱验证邮件
const sendVerificationEmail = async () => {
  try {
    sendingVerification.value = true
    await api.post('/api/profile/send-verification-email')
    ElMessage.success('验证邮件发送成功，请检查您的邮箱')
  } catch (error) {
    if (error.response?.data?.detail) {
      ElMessage.error(error.response.data.detail)
    } else {
      ElMessage.error('发送验证邮件失败')
    }
  } finally {
    sendingVerification.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadUserInfo()
})
</script>

<style scoped>
.profile-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.profile-header {
  margin-bottom: 30px;
  text-align: center;
}

.profile-header h2 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 28px;
}

.profile-subtitle {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.profile-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #2c3e50;
}

.email-verification {
  display: flex;
  align-items: center;
}

.info-text {
  color: #666;
  font-size: 14px;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-weight: bold;
  margin-right: 10px;
  min-width: 120px;
  color: #2c3e50;
}
</style>
