#!/usr/bin/env python3
"""
时区迁移脚本
将数据库中的UTC时间转换为中国时区时间
"""

import sqlite3
from datetime import datetime, timezone, timedelta
import os

# 中国时区
CHINA_TZ = timezone(timedelta(hours=8))

def migrate_timezone():
    """迁移时区数据"""
    db_path = "linuxdo_adt.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件 {db_path} 不存在")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 获取所有需要更新的表和字段
        tables_fields = [
            ("users", ["created_at", "updated_at"]),
            ("tasks", ["created_at", "updated_at"]),
            ("account_submissions", ["submitted_at"]),
            ("system_notices", ["created_at"])
        ]
        
        for table_name, fields in tables_fields:
            print(f"正在处理表: {table_name}")
            
            # 检查表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (table_name,))
            
            if not cursor.fetchone():
                print(f"表 {table_name} 不存在，跳过")
                continue
            
            for field in fields:
                # 检查字段是否存在
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [col[1] for col in cursor.fetchall()]
                
                if field not in columns:
                    print(f"字段 {table_name}.{field} 不存在，跳过")
                    continue
                
                print(f"  更新字段: {field}")
                
                # 获取所有记录
                cursor.execute(f"SELECT id, {field} FROM {table_name} WHERE {field} IS NOT NULL")
                records = cursor.fetchall()
                
                for record_id, time_str in records:
                    if not time_str:
                        continue
                    
                    try:
                        # 解析时间字符串
                        if 'T' in time_str:
                            # ISO格式
                            if time_str.endswith('Z'):
                                # UTC时间
                                dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
                            elif '+' in time_str or time_str.count('-') > 2:
                                # 已有时区信息
                                dt = datetime.fromisoformat(time_str)
                            else:
                                # 假设是UTC时间
                                dt = datetime.fromisoformat(time_str).replace(tzinfo=timezone.utc)
                        else:
                            # 简单格式，假设是UTC时间
                            dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=timezone.utc)
                        
                        # 转换为中国时区
                        china_dt = dt.astimezone(CHINA_TZ)
                        china_str = china_dt.isoformat()
                        
                        # 更新数据库
                        cursor.execute(f"""
                            UPDATE {table_name} 
                            SET {field} = ? 
                            WHERE id = ?
                        """, (china_str, record_id))
                        
                        print(f"    ID {record_id}: {time_str} -> {china_str}")
                        
                    except Exception as e:
                        print(f"    处理记录 ID {record_id} 时出错: {e}")
                        continue
        
        # 提交更改
        conn.commit()
        print("时区迁移完成！")
        
    except Exception as e:
        print(f"迁移过程中出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_timezone()
