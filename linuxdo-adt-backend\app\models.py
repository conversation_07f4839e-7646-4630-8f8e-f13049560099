from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime, timezone, timedelta
from .database import Base

# 定义东八区时区
CHINA_TZ = timezone(timedelta(hours=8))

def china_now():
    """返回中国时区的当前时间"""
    return datetime.now(CHINA_TZ)


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    role = Column(String(20), default="user")  # user, admin
    is_active = Column(Boolean, default=True)
    email_verified = Column(Boolean, default=False)  # 邮箱验证状态
    email_verified_at = Column(DateTime(timezone=True))  # 邮箱验证时间
    created_at = Column(DateTime(timezone=True), default=china_now)
    updated_at = Column(DateTime(timezone=True), onupdate=china_now)

    # 关系
    tasks = relationship("Task", back_populates="creator")


class Task(Base):
    __tablename__ = "tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False)
    description = Column(Text)
    task_type = Column(String(50), nullable=False)  # 升2级、升3级、保号等
    duration_days = Column(Integer, nullable=False)
    status = Column(String(20), default="待提交")  # 待提交、进行中、已完成、已取消
    share_token = Column(String(100), unique=True, index=True)
    creator_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), default=china_now)
    updated_at = Column(DateTime(timezone=True), onupdate=china_now)
    expires_at = Column(DateTime(timezone=True))
    
    # 关系
    creator = relationship("User", back_populates="tasks")
    account_submissions = relationship("AccountSubmission", back_populates="task")


class AccountSubmission(Base):
    __tablename__ = "account_submissions"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=False)
    username = Column(String(100), nullable=False)
    password = Column(String(255), nullable=False)
    email = Column(String(100), nullable=False)
    level_info = Column(String(100), nullable=True, default="")  # 等级信息
    key_info = Column(String(255), nullable=True, default="")   # key信息
    need_notification = Column(Boolean, default=True)           # 是否需要通知
    submitted_at = Column(DateTime(timezone=True), default=china_now)
    status = Column(String(20), default="进行中")  # 进行中、已完成、已取消

    # 关系
    task = relationship("Task", back_populates="account_submissions")


class SystemNotice(Base):
    __tablename__ = "system_notices"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    priority = Column(String(20), default="info")  # info, warning, error, success
    is_active = Column(Boolean, default=True)
    creator_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), default=china_now)
    updated_at = Column(DateTime(timezone=True), onupdate=china_now)

    # 关系
    creator = relationship("User")


class SystemSettings(Base):
    __tablename__ = "system_settings"

    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, nullable=False, index=True)
    value = Column(Text, nullable=False)
    description = Column(String(500))
    created_at = Column(DateTime(timezone=True), default=china_now)
    updated_at = Column(DateTime(timezone=True), onupdate=china_now)


class EmailTemplate(Base):
    __tablename__ = "email_templates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    subject = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    description = Column(String(500))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), default=china_now)
    updated_at = Column(DateTime(timezone=True), onupdate=china_now)


class EmailLog(Base):
    __tablename__ = "email_logs"

    id = Column(Integer, primary_key=True, index=True)
    to_email = Column(String(100), nullable=False)
    subject = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    template_name = Column(String(100))
    status = Column(String(20), default="pending")  # pending, sent, failed
    error_message = Column(Text)
    sent_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), default=china_now)


class Hitokoto(Base):
    __tablename__ = "hitokoto"

    id = Column(Integer, primary_key=True, index=True)
    hitokoto_id = Column(Integer, index=True)  # 一言标识
    uuid = Column(String(100), unique=True, nullable=False, index=True)  # 一言唯一标识
    hitokoto = Column(Text, nullable=False)  # 一言正文
    type = Column(String(10))  # 类型
    from_text = Column(String(500))  # 一言的出处 (from是关键字，用from_text)
    from_who = Column(String(200))  # 一言的作者
    creator = Column(String(200))  # 添加者
    creator_uid = Column(Integer)  # 添加者用户标识
    reviewer = Column(Integer)  # 审核员标识
    commit_from = Column(String(100))  # 提交方式
    hitokoto_created_at = Column(String(100))  # 一言添加时间
    length = Column(Integer)  # 句子长度
    created_at = Column(DateTime(timezone=True), default=china_now)  # 本地存储时间
