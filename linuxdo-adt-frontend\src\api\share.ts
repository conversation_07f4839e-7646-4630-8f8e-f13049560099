import api from './index'

export interface SharedTaskInfo {
  id: number
  title: string
  description?: string
  task_type: string
  duration_days: number
  created_at: string
  is_submitted: boolean
}

export interface AccountSubmission {
  username: string
  password: string
  email: string
  level_info: string
  key_info: string
  need_notification: boolean
}

export interface SystemNotice {
  id: number
  title: string
  content: string
  priority: string
  is_active: boolean
  created_at: string
}

// 获取分享任务信息
export const getSharedTask = (shareToken: string) => {
  return api.get<SharedTaskInfo>(`/api/share/${shareToken}`)
}

// 提交账号信息
export const submitAccountInfo = (shareToken: string, data: AccountSubmission) => {
  return api.post(`/api/share/${shareToken}/submit`, data)
}

// 获取系统提示信息
export const getNoticesForShare = (shareToken: string) => {
  return api.get<SystemNotice[]>(`/api/share/${shareToken}/notices`)
}
