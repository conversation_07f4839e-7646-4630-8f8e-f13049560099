<template>
  <div class="account-list">
    <div class="header-actions">
      <h3>账号管理</h3>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        手动添加账号
      </el-button>
    </div>

    <!-- 搜索和过滤区域 -->
    <div class="search-filters">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchText"
            placeholder="搜索用户名或邮箱"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="filterStatus"
            placeholder="账号状态"
            clearable
            @change="handleFilter"
          >
            <el-option label="进行中" value="进行中" />
            <el-option label="已完成" value="已完成" />
            <el-option label="已取消" value="已取消" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="filterNotification"
            placeholder="通知设置"
            clearable
            @change="handleFilter"
          >
            <el-option label="接收通知" value="true" />
            <el-option label="不接收通知" value="false" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filterLevel"
            placeholder="等级信息"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="3">
          <el-button @click="resetFilters">重置</el-button>
        </el-col>
        <el-col :span="3">
          <el-button type="info" @click="exportData">导出</el-button>
        </el-col>
      </el-row>
    </div>
    
    <el-table :data="accounts" v-loading="loading" style="width: 100%">
      <el-table-column label="序号" width="80" type="index" :index="(index: number) => (currentPage - 1) * pageSize + index + 1" />

      <el-table-column prop="username" label="LD用户名" width="150" show-overflow-tooltip />
      <el-table-column prop="password" label="LD密码" width="150" show-overflow-tooltip />
      <el-table-column prop="email" label="LD邮箱" min-width="200" show-overflow-tooltip />
      <!-- 管理员可以看到任务信息和创建者 -->
      <el-table-column v-if="isAdmin" prop="task_title" label="任务标题" width="180" show-overflow-tooltip />
      <el-table-column v-if="isAdmin" prop="task_creator_username" label="任务创建者" width="120" />
      <el-table-column prop="level_info" label="等级信息" width="120" />
      <el-table-column prop="key_info" label="Key信息" width="150" show-overflow-tooltip />
      <el-table-column prop="need_notification" label="通知设置" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.need_notification ? 'success' : 'info'">
            {{ scope.row.need_notification ? '接收通知' : '不接收通知' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="120">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="submitted_at" label="提交时间" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.submitted_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="scope">
          <el-button size="small" type="primary" @click="editAccount(scope.row)">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-select
            v-model="scope.row.status"
            @change="updateStatus(scope.row)"
            size="small"
            style="width: 120px; margin-left: 10px"
          >
            <el-option label="进行中" value="进行中" />
            <el-option label="已完成" value="已完成" />
            <el-option label="已取消" value="已取消" />
          </el-select>
          <el-button size="small" type="info" @click="viewTaskInfo(scope.row)" style="margin-left: 10px">
            <el-icon><View /></el-icon>
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 编辑账号信息对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑账号信息" width="500px">
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="120px">
        <el-form-item label="LinuxDo用户名" prop="username">
          <el-input v-model="editForm.username" placeholder="请输入LinuxDo用户名" />
        </el-form-item>
        <el-form-item label="LinuxDo密码" prop="password">
          <el-input v-model="editForm.password" type="password" placeholder="请输入LinuxDo密码" show-password />
        </el-form-item>
        <el-form-item label="LinuxDo邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入LinuxDo邮箱" />
        </el-form-item>
        <el-form-item label="等级信息">
          <el-input v-model="editForm.level_info" placeholder="请输入等级信息（可选）" />
        </el-form-item>
        <el-form-item label="Key信息">
          <el-input v-model="editForm.key_info" placeholder="请输入Key信息（可选）" />
        </el-form-item>
        <el-form-item label="通知设置">
          <el-switch
            v-model="editForm.need_notification"
            active-text="接收通知"
            inactive-text="不接收通知"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleUpdateAccount" :loading="updating">保存</el-button>
      </template>
    </el-dialog>

    <!-- 手动添加账号对话框 -->
    <el-dialog v-model="showAddDialog" title="手动添加账号" width="500px">
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="120px">
        <el-form-item label="LinuxDo用户名" prop="username">
          <el-input v-model="addForm.username" placeholder="请输入LinuxDo用户名" />
        </el-form-item>
        <el-form-item label="LinuxDo密码" prop="password">
          <el-input v-model="addForm.password" type="password" placeholder="请输入LinuxDo密码" show-password />
        </el-form-item>
        <el-form-item label="LinuxDo邮箱" prop="email">
          <el-input v-model="addForm.email" placeholder="请输入LinuxDo邮箱" />
        </el-form-item>
        <el-form-item label="任务类型" prop="task_type">
          <el-select v-model="addForm.task_type" placeholder="请选择任务类型">
            <el-option label="升2级" value="升2级" />
            <el-option label="升3级" value="升3级" />
            <el-option label="保号" value="保号" />
          </el-select>
        </el-form-item>
        <el-form-item label="代挂天数" prop="duration_days">
          <el-input-number v-model="addForm.duration_days" :min="1" :max="365" />
        </el-form-item>
        <el-form-item label="任务描述">
          <el-input v-model="addForm.description" type="textarea" placeholder="请输入任务描述（可选）" />
        </el-form-item>
        <el-form-item label="等级信息">
          <el-input v-model="addForm.level_info" placeholder="请输入等级信息（可选）" />
        </el-form-item>
        <el-form-item label="Key信息">
          <el-input v-model="addForm.key_info" placeholder="请输入Key信息（可选）" />
        </el-form-item>
        <el-form-item label="通知设置">
          <el-switch
            v-model="addForm.need_notification"
            active-text="接收通知"
            inactive-text="不接收通知"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="handleAddAccount" :loading="adding">添加</el-button>
      </template>
    </el-dialog>

    <!-- 任务信息模态框 -->
    <el-dialog v-model="showTaskInfoDialog" title="任务信息" width="600px">
      <div v-if="currentTaskInfo">
        <el-descriptions :column="2" border class="task-info-descriptions">
          <el-descriptions-item label="任务ID">{{ currentTaskInfo.id }}</el-descriptions-item>
          <el-descriptions-item label="任务标题">{{ currentTaskInfo.title }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">{{ currentTaskInfo.task_type }}</el-descriptions-item>
          <el-descriptions-item label="代挂天数">{{ currentTaskInfo.duration_days }}天</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="getTaskStatusType(currentTaskInfo.status)">
              {{ currentTaskInfo.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(currentTaskInfo.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="过期时间" v-if="currentTaskInfo.expires_at">
            {{ formatDate(currentTaskInfo.expires_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="2" v-if="currentTaskInfo.description">
            {{ currentTaskInfo.description }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 关联账号信息 -->
        <el-divider content-position="left">关联账号信息</el-divider>
        <div v-if="relatedAccount">
          <el-descriptions :column="2" border class="account-info-descriptions">
            <el-descriptions-item label="LD用户名">{{ relatedAccount.username }}</el-descriptions-item>
            <el-descriptions-item label="LD密码">{{ relatedAccount.password }}</el-descriptions-item>
            <el-descriptions-item label="LD邮箱">{{ relatedAccount.email }}</el-descriptions-item>
            <el-descriptions-item label="等级信息">{{ relatedAccount.level_info || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="Key信息">
              <span style="word-break: break-all;">{{ relatedAccount.key_info || '暂无' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="通知设置">
              <el-tag :type="relatedAccount.need_notification ? 'success' : 'info'">
                {{ relatedAccount.need_notification ? '接收通知' : '不接收通知' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="账号状态">
              <el-tag :type="getStatusType(relatedAccount.status)">
                {{ relatedAccount.status }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="提交时间" :span="2">
              {{ formatDate(relatedAccount.submitted_at) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div v-else>
          <el-empty description="暂无关联账号信息" :image-size="80" />
        </div>
      </div>
      <template #footer>
        <el-button @click="showTaskInfoDialog = false">关闭</el-button>
        <el-button type="primary" @click="goToTaskPage">跳转到任务页面</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Plus, View, Search } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import api from '../api'

interface AccountSubmission {
  id: number
  task_id: number
  username: string
  password: string
  email: string
  level_info: string
  key_info: string
  need_notification: boolean
  submitted_at: string
  status: string
  // 任务相关信息
  task_title?: string
  task_type?: string
  task_creator_username?: string
}

interface AdminAccountCreate {
  username: string
  password: string
  email: string
  level_info: string
  key_info: string
  need_notification: boolean
  task_type: string
  duration_days: number
  description: string
}

interface TaskInfo {
  id: number
  title: string
  description?: string
  task_type: string
  duration_days: number
  status: string
  share_token: string
  creator_id: number
  created_at: string
  updated_at?: string
  expires_at?: string
}

const accounts = ref<AccountSubmission[]>([])
const allAccounts = ref<AccountSubmission[]>([]) // 存储所有账号数据
const loading = ref(false)
const showEditDialog = ref(false)
const updating = ref(false)
const currentEditingAccount = ref<AccountSubmission | null>(null)

// 当前用户信息
const currentUser = ref<any>(null)
const isAdmin = ref(false)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(50)
const total = ref(0)

// 搜索和过滤相关
const searchText = ref('')
const filterStatus = ref('')
const filterNotification = ref('')
const filterLevel = ref('')

// 添加账号相关
const showAddDialog = ref(false)
const adding = ref(false)

// 任务信息相关
const showTaskInfoDialog = ref(false)
const currentTaskInfo = ref<TaskInfo | null>(null)
const relatedAccount = ref<AccountSubmission | null>(null)

// 路由器
const router = useRouter()

const editForm = reactive({
  id: 0,
  username: '',
  password: '',
  email: '',
  level_info: '',
  key_info: '',
  need_notification: true
})

const editRules = {
  username: [{ required: true, message: '请输入LinuxDo用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入LinuxDo密码', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入LinuxDo邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

const addForm = reactive<AdminAccountCreate>({
  username: '',
  password: '',
  email: '',
  level_info: '',
  key_info: '',
  need_notification: true,
  task_type: '',
  duration_days: 1,
  description: ''
})

const addRules = {
  username: [{ required: true, message: '请输入LinuxDo用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入LinuxDo密码', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入LinuxDo邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  task_type: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
  duration_days: [{ required: true, message: '请输入代挂天数', trigger: 'blur' }]
}

const editFormRef = ref()
const addFormRef = ref()

const loadAccounts = async () => {
  loading.value = true
  try {
    // 获取所有数据用于前端过滤
    const response = await api.get<any>('/api/accounts', {
      params: { skip: 0, limit: 10000 }
    })

    // 适应新的API响应格式
    if (response.data.items) {
      allAccounts.value = response.data.items
    } else {
      // 兼容旧格式
      allAccounts.value = response.data
    }

    // 应用过滤和搜索
    applyFilters()
  } catch (error) {
    ElMessage.error('加载账号列表失败')
  } finally {
    loading.value = false
  }
}

// 应用过滤和搜索
const applyFilters = () => {
  let filteredAccounts = [...allAccounts.value]

  // 搜索过滤
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase()
    filteredAccounts = filteredAccounts.filter(account =>
      account.username.toLowerCase().includes(searchLower) ||
      account.email.toLowerCase().includes(searchLower)
    )
  }

  // 状态过滤
  if (filterStatus.value) {
    filteredAccounts = filteredAccounts.filter(account => account.status === filterStatus.value)
  }

  // 通知设置过滤
  if (filterNotification.value) {
    const needNotification = filterNotification.value === 'true'
    filteredAccounts = filteredAccounts.filter(account => account.need_notification === needNotification)
  }

  // 等级信息过滤
  if (filterLevel.value) {
    const levelLower = filterLevel.value.toLowerCase()
    filteredAccounts = filteredAccounts.filter(account =>
      account.level_info.toLowerCase().includes(levelLower)
    )
  }

  // 更新总数和分页
  total.value = filteredAccounts.length

  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  accounts.value = filteredAccounts.slice(start, end)
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1 // 重置到第一页
  applyFilters()
}

// 过滤处理
const handleFilter = () => {
  currentPage.value = 1 // 重置到第一页
  applyFilters()
}

// 重置过滤器
const resetFilters = () => {
  searchText.value = ''
  filterStatus.value = ''
  filterNotification.value = ''
  filterLevel.value = ''
  currentPage.value = 1
  applyFilters()
}

// 导出数据
const exportData = () => {
  // 获取当前过滤后的数据
  let filteredAccounts = [...allAccounts.value]

  // 应用相同的过滤逻辑
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase()
    filteredAccounts = filteredAccounts.filter(account =>
      account.username.toLowerCase().includes(searchLower) ||
      account.email.toLowerCase().includes(searchLower)
    )
  }

  if (filterStatus.value) {
    filteredAccounts = filteredAccounts.filter(account => account.status === filterStatus.value)
  }

  if (filterNotification.value) {
    const needNotification = filterNotification.value === 'true'
    filteredAccounts = filteredAccounts.filter(account => account.need_notification === needNotification)
  }

  if (filterLevel.value) {
    const levelLower = filterLevel.value.toLowerCase()
    filteredAccounts = filteredAccounts.filter(account =>
      account.level_info.toLowerCase().includes(levelLower)
    )
  }

  // 创建CSV内容
  const headers = ['序号', 'LD用户名', 'LD密码', 'LD邮箱', '等级信息', 'Key信息', '通知设置', '状态', '提交时间']
  const csvContent = [
    headers.join(','),
    ...filteredAccounts.map((account, index) => [
      index + 1,
      account.username,
      account.password,
      account.email,
      account.level_info,
      account.key_info,
      account.need_notification ? '接收通知' : '不接收通知',
      account.status,
      new Date(account.submitted_at).toLocaleString('zh-CN')
    ].join(','))
  ].join('\n')

  // 下载文件
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `账号列表_${new Date().toISOString().slice(0, 10)}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  ElMessage.success(`已导出 ${filteredAccounts.length} 条记录`)
}

// 分页事件处理
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1
  applyFilters()
}

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  applyFilters()
}

const updateStatus = async (account: AccountSubmission) => {
  try {
    await api.put(`/api/accounts/${account.id}/status`, null, {
      params: { status: account.status }
    })
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
    loadAccounts() // 重新加载数据
  }
}

const editAccount = (account: AccountSubmission) => {
  currentEditingAccount.value = account
  Object.assign(editForm, {
    id: account.id,
    username: account.username,
    password: account.password,
    email: account.email,
    level_info: account.level_info,
    key_info: account.key_info,
    need_notification: account.need_notification
  })
  showEditDialog.value = true
}

const handleUpdateAccount = async () => {
  if (!editFormRef.value) return

  await editFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      updating.value = true
      try {
        await api.put(`/api/accounts/${editForm.id}`, {
          username: editForm.username,
          password: editForm.password,
          email: editForm.email,
          level_info: editForm.level_info,
          key_info: editForm.key_info,
          need_notification: editForm.need_notification
        })
        ElMessage.success('账号信息更新成功')
        showEditDialog.value = false
        loadAccounts() // 重新加载数据
      } catch (error: any) {
        ElMessage.error(error.response?.data?.detail || '更新失败')
      } finally {
        updating.value = false
      }
    }
  })
}

const handleAddAccount = async () => {
  if (!addFormRef.value) return

  await addFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      adding.value = true
      try {
        await api.post('/api/accounts/admin/create', {
          username: addForm.username,
          password: addForm.password,
          email: addForm.email,
          level_info: addForm.level_info,
          key_info: addForm.key_info,
          need_notification: addForm.need_notification,
          task_type: addForm.task_type,
          duration_days: addForm.duration_days,
          description: addForm.description
        })
        ElMessage.success('账号添加成功')
        showAddDialog.value = false
        // 重置表单
        Object.assign(addForm, {
          username: '',
          password: '',
          email: '',
          level_info: '',
          key_info: '',
          need_notification: true,
          task_type: '',
          duration_days: 1,
          description: ''
        })
        loadAccounts() // 重新加载数据
      } catch (error: any) {
        ElMessage.error(error.response?.data?.detail || '添加失败')
      } finally {
        adding.value = false
      }
    }
  })
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '进行中': 'warning',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return statusMap[status] || 'info'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 查看任务信息
const viewTaskInfo = async (account: AccountSubmission) => {
  try {
    // 获取任务信息
    const taskResponse = await api.get<TaskInfo>(`/api/tasks/${account.task_id}`)
    currentTaskInfo.value = taskResponse.data

    // 设置关联的账号信息（就是当前点击的账号）
    relatedAccount.value = account

    showTaskInfoDialog.value = true
  } catch (error) {
    ElMessage.error('获取任务信息失败')
  }
}

// 任务状态类型映射
const getTaskStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待提交': 'info',
    '进行中': 'warning',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return statusMap[status] || 'info'
}

// 跳转到任务页面并高亮
const goToTaskPage = () => {
  if (currentTaskInfo.value) {
    // 关闭模态框
    showTaskInfoDialog.value = false

    // 跳转到任务页面，并传递高亮参数
    router.push({
      name: 'TaskList',
      query: { highlight: currentTaskInfo.value.id.toString() }
    })
  }
}

// 获取当前用户信息
const getCurrentUser = async () => {
  try {
    const response = await api.get('/api/auth/me')
    currentUser.value = response.data
    isAdmin.value = response.data.role === 'admin'
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

onMounted(async () => {
  await getCurrentUser()
  loadAccounts()
})
</script>

<style scoped>
.account-list {
  background: white;
  padding: 20px;
  border-radius: 8px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions h3 {
  margin: 0;
}

/* 搜索过滤区域样式 */
.search-filters {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 确保模态框中描述列表的标签和内容宽度完全一致 */
:deep(.task-info-descriptions),
:deep(.account-info-descriptions) {
  table-layout: fixed !important;
}

:deep(.task-info-descriptions .el-descriptions__table),
:deep(.account-info-descriptions .el-descriptions__table) {
  table-layout: fixed !important;
  width: 100% !important;
}

/* 每个单元格占50%宽度 */
:deep(.task-info-descriptions .el-descriptions__cell),
:deep(.account-info-descriptions .el-descriptions__cell) {
  width: 50% !important;
}

/* 标签列固定宽度 */
:deep(.task-info-descriptions .el-descriptions__label),
:deep(.account-info-descriptions .el-descriptions__label) {
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  vertical-align: top !important;
}

/* 内容列自适应剩余宽度 */
:deep(.task-info-descriptions .el-descriptions__content),
:deep(.account-info-descriptions .el-descriptions__content) {
  width: calc(100% - 100px) !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  vertical-align: top !important;
}
</style>
