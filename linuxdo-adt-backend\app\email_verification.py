"""
邮箱验证服务
用于生成验证令牌和发送验证邮件
"""

import secrets
import logging
from typing import Optional
from datetime import datetime, timedelta, timezone
from sqlalchemy.orm import Session
from .models import User
from .email_hooks import email_hooks

logger = logging.getLogger(__name__)

# 定义东八区时区
CHINA_TZ = timezone(timedelta(hours=8))


class EmailVerificationService:
    """邮箱验证服务"""
    
    def __init__(self):
        # 验证令牌存储（实际项目中应该使用Redis或数据库）
        self.verification_tokens = {}
    
    def generate_verification_token(self, user_id: int, email: str, expires_hours: int = 24) -> str:
        """
        生成邮箱验证令牌
        
        Args:
            user_id: 用户ID
            email: 要验证的邮箱
            expires_hours: 过期时间（小时）
        
        Returns:
            str: 验证令牌
        """
        token = secrets.token_urlsafe(32)
        expires_at = datetime.now(CHINA_TZ) + timedelta(hours=expires_hours)
        
        self.verification_tokens[token] = {
            "user_id": user_id,
            "email": email,
            "expires_at": expires_at,
            "created_at": datetime.now(CHINA_TZ)
        }
        
        logger.info(f"为用户 {user_id} 生成邮箱验证令牌，邮箱: {email}")
        return token
    
    def verify_token(self, token: str) -> Optional[dict]:
        """
        验证令牌
        
        Args:
            token: 验证令牌
        
        Returns:
            dict: 令牌信息，如果无效返回None
        """
        token_info = self.verification_tokens.get(token)
        if not token_info:
            return None
        
        # 检查是否过期
        if datetime.now(CHINA_TZ) > token_info["expires_at"]:
            # 删除过期令牌
            del self.verification_tokens[token]
            return None
        
        return token_info
    
    def consume_token(self, token: str) -> Optional[dict]:
        """
        消费令牌（验证后删除）
        
        Args:
            token: 验证令牌
        
        Returns:
            dict: 令牌信息，如果无效返回None
        """
        token_info = self.verify_token(token)
        if token_info:
            # 删除已使用的令牌
            del self.verification_tokens[token]
        
        return token_info
    
    async def send_verification_email(
        self, 
        user: User, 
        email_to_verify: Optional[str] = None,
        verification_url_base: str = "http://localhost:5173/verify-email",
        db: Optional[Session] = None
    ) -> bool:
        """
        发送邮箱验证邮件
        
        Args:
            user: 用户对象
            email_to_verify: 要验证的邮箱（如果为空则验证用户邮箱）
            verification_url_base: 验证链接基础URL
            db: 数据库会话
        
        Returns:
            bool: 是否发送成功
        """
        if not db:
            logger.error("数据库会话为空，无法发送验证邮件")
            return False
        
        target_email = email_to_verify or user.email
        
        # 生成验证令牌
        token = self.generate_verification_token(user.id, target_email)
        verification_url = f"{verification_url_base}?token={token}"
        
        # 发送验证邮件
        try:
            success = await email_hooks.trigger(
                "email_verification",
                db=db,
                user=user,
                verification_token=token,
                verification_url=verification_url,
                email_to_verify=target_email
            )
            
            if success:
                logger.info(f"邮箱验证邮件发送成功: {target_email}")
            else:
                logger.error(f"邮箱验证邮件发送失败: {target_email}")
            
            return success
            
        except Exception as e:
            logger.error(f"发送邮箱验证邮件时发生错误: {str(e)}")
            return False
    
    async def send_registration_verification(
        self, 
        user: User, 
        db: Session
    ) -> bool:
        """
        发送注册邮箱验证邮件
        
        Args:
            user: 新注册的用户
            db: 数据库会话
        
        Returns:
            bool: 是否发送成功
        """
        return await self.send_verification_email(
            user=user,
            email_to_verify=user.email,
            verification_url_base="http://localhost:5173/verify-registration",
            db=db
        )
    
    async def send_account_email_verification(
        self, 
        user: User, 
        account_email: str, 
        db: Session
    ) -> bool:
        """
        发送账号提交邮箱验证邮件
        
        Args:
            user: 用户对象
            account_email: 提交的账号邮箱
            db: 数据库会话
        
        Returns:
            bool: 是否发送成功
        """
        return await self.send_verification_email(
            user=user,
            email_to_verify=account_email,
            verification_url_base="http://localhost:5173/verify-account-email",
            db=db
        )
    
    def cleanup_expired_tokens(self):
        """清理过期的验证令牌"""
        current_time = datetime.now(CHINA_TZ)
        expired_tokens = []
        
        for token, token_info in self.verification_tokens.items():
            if current_time > token_info["expires_at"]:
                expired_tokens.append(token)
        
        for token in expired_tokens:
            del self.verification_tokens[token]
        
        if expired_tokens:
            logger.info(f"清理了 {len(expired_tokens)} 个过期的验证令牌")
    
    def get_token_stats(self) -> dict:
        """获取令牌统计信息"""
        current_time = datetime.now(CHINA_TZ)
        total_tokens = len(self.verification_tokens)
        expired_tokens = 0
        
        for token_info in self.verification_tokens.values():
            if current_time > token_info["expires_at"]:
                expired_tokens += 1
        
        return {
            "total_tokens": total_tokens,
            "active_tokens": total_tokens - expired_tokens,
            "expired_tokens": expired_tokens
        }


# 全局邮箱验证服务实例
email_verification_service = EmailVerificationService()
